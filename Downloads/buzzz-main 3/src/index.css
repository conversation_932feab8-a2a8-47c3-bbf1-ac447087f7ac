@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom Animations */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-in-right {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slide-in-left {
  from {
    opacity: 0;
    transform: translateX(-100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scale-in {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slide-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.3s ease-out;
}

.animate-slide-in-right {
  animation: slide-in-right 0.4s ease-out;
}

.animate-slide-in-left {
  animation: slide-in-left 0.4s ease-out;
}

.animate-scale-in {
  animation: scale-in 0.3s ease-out;
}

.animate-slide-in {
  animation: slide-in 0.3s ease-out;
}

/* Golden Accent Classes */
.golden-gradient {
  background: linear-gradient(135deg, #f5b700 0%, #f8d86b 50%, #f5b700 100%);
}

.golden-glow {
  box-shadow: 0 0 20px rgba(245, 183, 0, 0.3);
}

.golden-border {
  border: 2px solid #f5b700;
}

.golden-text-shadow {
  text-shadow: 0 2px 4px rgba(245, 183, 0, 0.3);
}

.golden-highlight {
  background: linear-gradient(120deg, transparent 0%, rgba(245, 183, 0, 0.1) 50%, transparent 100%);
}

.golden-accent-border {
  border-left: 4px solid #f5b700;
}

.golden-button {
  background: linear-gradient(135deg, #f5b700 0%, #e6a800 100%);
  color: white;
  transition: all 0.3s ease;
}

.golden-button:hover {
  background: linear-gradient(135deg, #e6a800 0%, #c28a00 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(245, 183, 0, 0.4);
}

/* Safe Area Support for Modern Mobile Devices */
@supports (padding: max(0px)) {
  .safe-area-top {
    padding-top: max(1rem, env(safe-area-inset-top));
  }
  
  .safe-area-bottom {
    padding-bottom: max(1.25rem, env(safe-area-inset-bottom));
  }
  
  .safe-area-left {
    padding-left: max(1rem, env(safe-area-inset-left));
  }
  
  .safe-area-right {
    padding-right: max(1rem, env(safe-area-inset-right));
  }
}

/* Mobile-specific optimizations */
@media (max-width: 768px) {
  .mobile-cta-container {
    padding-bottom: max(1.25rem, env(safe-area-inset-bottom));
  }
  
  .mobile-content {
    padding-top: max(1rem, env(safe-area-inset-top));
  }
  
  /* Improve touch targets for mobile */
  button, a {
    min-height: 44px;
    min-width: 44px;
  }
  
  /* Better sidebar scrolling on mobile */
  .sidebar-mobile-scroll {
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }
  
  .sidebar-mobile-scroll::-webkit-scrollbar {
    display: none;
  }
  
  /* Prevent body scroll when sidebar is open */
  body.sidebar-open {
    overflow: hidden;
  }
  
  /* Improve mobile navigation touch targets */
  .mobile-nav-item {
    padding: 12px 16px;
    margin: 2px 0;
  }
  
  /* Better mobile spacing */
  .mobile-safe-padding {
    padding-left: max(1rem, env(safe-area-inset-left));
    padding-right: max(1rem, env(safe-area-inset-right));
  }
}

/* Quill Editor Customization */
.ql-editor {
  min-height: 200px;
  font-family: inherit;
  line-height: 1.6;
  font-size: 16px; /* Increased from default 14px */
}

.ql-toolbar {
  border-top: 1px solid #e5e7eb;
  border-left: 1px solid #e5e7eb;
  border-right: 1px solid #e5e7eb;
  border-bottom: none;
  background: #f9fafb;
}

.ql-container {
  border-bottom: 1px solid #e5e7eb;
  border-left: 1px solid #e5e7eb;
  border-right: 1px solid #e5e7eb;
  border-top: none;
}

.ql-editor.ql-blank::before {
  color: #9ca3af;
  font-style: normal;
  font-size: 16px; /* Match the editor font size */
}

/* Fix Quill Editor Lists - Bullet Points and Numbering */
.ql-editor ul {
  padding-left: 1.5rem;
  margin-bottom: 1rem;
  list-style-type: disc;
}

.ql-editor ol {
  padding-left: 1.5rem;
  margin-bottom: 1rem;
  list-style-type: decimal;
}

.ql-editor ul li {
  display: list-item;
  list-style-type: disc;
  list-style-position: outside;
  margin-bottom: 0.5rem;
  padding-left: 0.25rem;
}

.ql-editor ol li {
  display: list-item;
  list-style-type: decimal;
  list-style-position: outside;
  margin-bottom: 0.5rem;
  padding-left: 0.25rem;
}

/* Nested lists */
.ql-editor ul ul {
  list-style-type: circle;
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

.ql-editor ul ul ul {
  list-style-type: square;
}

.ql-editor ol ol {
  list-style-type: lower-alpha;
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

.ql-editor ol ol ol {
  list-style-type: lower-roman;
}

/* Blockquote styling in editor */
.ql-editor blockquote {
  border-left: 4px solid #e5e7eb;
  padding-left: 1rem;
  margin: 1rem 0;
  font-style: italic;
  background-color: #f9fafb;
  padding: 1rem;
  border-radius: 0.375rem;
}

/* Code block styling in editor */
.ql-editor pre {
  background-color: #f3f4f6;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  padding: 1rem;
  margin: 1rem 0;
  overflow-x: auto;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.4;
}

/* Inline code styling in editor */
.ql-editor code {
  background-color: #f3f4f6;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-size: 0.875em;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

/* Prose styling for landing page content */
.prose {
  max-width: none;
}

.prose h1 {
  font-size: 1.875rem;
  font-weight: 700;
  margin-bottom: 1rem;
  line-height: 1.2;
}

.prose h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  margin-top: 2rem;
  line-height: 1.3;
  padding-top: 0.5rem;
  padding-bottom: 0.25rem;
}

.prose h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  margin-top: 1.75rem;
  line-height: 1.4;
  padding-top: 0.375rem;
  padding-bottom: 0.25rem;
}

.prose h4 {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  margin-top: 1.5rem;
  line-height: 1.4;
  padding-top: 0.25rem;
  padding-bottom: 0.125rem;
}

.prose h5 {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  margin-top: 1.25rem;
  line-height: 1.5;
  padding-top: 0.25rem;
  padding-bottom: 0.125rem;
}

.prose h6 {
  font-size: 0.875rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  margin-top: 1rem;
  line-height: 1.5;
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
}

.prose p {
  margin-bottom: 1rem;
  line-height: 1.7;
  font-size: 16px; /* Ensure consistent font size */
}

.prose ul, .prose ol {
  margin-bottom: 1rem;
  padding-left: 1.5rem;
}

.prose ul {
  list-style-type: disc;
}

.prose ol {
  list-style-type: decimal;
}

.prose li {
  margin-bottom: 0.5rem;
  line-height: 1.6;
  font-size: 16px; /* Consistent font size for list items */
  display: list-item;
  list-style-position: outside;
}

/* Nested list styling for prose */
.prose ul ul {
  list-style-type: circle;
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

.prose ul ul ul {
  list-style-type: square;
}

.prose ol ol {
  list-style-type: lower-alpha;
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

.prose ol ol ol {
  list-style-type: lower-roman;
}

.prose blockquote {
  border-left: 4px solid currentColor;
  padding-left: 1rem;
  margin: 1.5rem 0;
  font-style: italic;
  opacity: 0.8;
  font-size: 16px; /* Consistent font size */
}

.prose img {
  max-width: 100%;
  height: auto;
  border-radius: 0.5rem;
  margin: 1.5rem 0;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.prose a {
  text-decoration: underline;
  text-underline-offset: 2px;
}

.prose a:hover {
  opacity: 0.8;
}

.prose strong {
  font-weight: 600;
}

.prose em {
  font-style: italic;
}

.prose code {
  background-color: rgba(0, 0, 0, 0.1);
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-size: 0.875em;
}

.prose pre {
  background-color: rgba(0, 0, 0, 0.05);
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  margin: 1rem 0;
}

/* Ensure all text in the editor has consistent sizing */
.ql-editor * {
  font-size: inherit !important;
}

/* Override Quill's default font sizes for better consistency */
.ql-editor h1 {
  font-size: 1.875rem !important;
  font-weight: 700;
  margin-bottom: 1rem;
  margin-top: 1.5rem;
  line-height: 1.2;
}

.ql-editor h2 {
  font-size: 1.5rem !important;
  font-weight: 600;
  margin-bottom: 1rem;
  margin-top: 2rem;
  line-height: 1.3;
  padding-top: 0.5rem;
  padding-bottom: 0.25rem;
}

.ql-editor h3 {
  font-size: 1.25rem !important;
  font-weight: 600;
  margin-bottom: 0.75rem;
  margin-top: 1.75rem;
  line-height: 1.4;
  padding-top: 0.375rem;
  padding-bottom: 0.25rem;
}

.ql-editor h4 {
  font-size: 1.125rem !important;
  font-weight: 600;
  margin-bottom: 0.5rem;
  margin-top: 1.5rem;
  line-height: 1.4;
  padding-top: 0.25rem;
  padding-bottom: 0.125rem;
}

.ql-editor h5 {
  font-size: 1rem !important;
  font-weight: 600;
  margin-bottom: 0.5rem;
  margin-top: 1.25rem;
  line-height: 1.5;
  padding-top: 0.25rem;
  padding-bottom: 0.125rem;
}

.ql-editor h6 {
  font-size: 0.875rem !important;
  font-weight: 600;
  margin-bottom: 0.5rem;
  margin-top: 1rem;
  line-height: 1.5;
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
}

.ql-editor p, .ql-editor div, .ql-editor span {
  font-size: 16px !important;
  line-height: 1.6;
  margin-bottom: 1rem;
}

.ql-editor ul li, .ql-editor ol li {
  font-size: 16px !important;
  line-height: 1.6;
}

/* Better spacing for editor content */
.ql-editor p:last-child {
  margin-bottom: 0;
}

/* Improve link styling in editor */
.ql-editor a {
  color: #2563eb;
  text-decoration: underline;
  text-underline-offset: 2px;
}

.ql-editor a:hover {
  color: #1d4ed8;
  opacity: 0.8;
}

/* Better image handling in editor */
.ql-editor img {
  max-width: 100%;
  height: auto;
  border-radius: 0.375rem;
  margin: 1rem 0;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

/* Custom Divider Styles */
.ql-editor hr {
  border: none;
  border-top: 2px solid #e5e7eb;
  margin: 1.5rem 0;
  height: 1px;
  background: none;
}

.prose hr {
  border: none;
  border-top: 2px solid #e5e7eb;
  margin: 1.5rem 0;
  height: 1px;
  background: none;
}

/* Custom Divider Button in Toolbar */
.ql-toolbar .ql-divider {
  width: 28px;
  height: 28px;
  position: relative;
  border: 1px solid transparent;
  border-radius: 3px;
}

.ql-toolbar .ql-divider:before {
  content: "━━";
  font-size: 12px;
  font-weight: bold;
  line-height: 28px;
  color: #444;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  letter-spacing: -2px;
}

.ql-toolbar .ql-divider:hover {
  border-color: #ccc;
  background-color: #f0f0f0;
}

.ql-toolbar .ql-divider:hover:before {
  color: #06c;
}

.ql-toolbar .ql-divider.ql-active {
  background-color: #e6f3ff;
  border-color: #06c;
}

.ql-toolbar .ql-divider.ql-active:before {
  color: #06c;
}

/* Social Icon Hover Effects */
.social-icon-hover:hover {
  background: var(--hover-bg) !important;
}

.social-icon-hover:hover .social-icon {
  color: var(--hover-text) !important;
  transform: scale(1.1);
}