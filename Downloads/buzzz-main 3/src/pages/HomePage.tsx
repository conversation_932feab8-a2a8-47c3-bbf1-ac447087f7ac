import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import Footer from '../components/Footer';
import AuthModal from '../components/AuthModal';

export default function HomePage() {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isHovering, setIsHovering] = useState(false);
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };
    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  return (
    <div className="min-h-screen relative overflow-hidden bg-black">
      {/* Animated Background */}
      <div className="fixed inset-0 bg-gradient-to-br from-purple-900 via-blue-900 to-pink-900">
        <div className="absolute inset-0 opacity-30">
          <div className="absolute inset-0" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
          }}></div>
        </div>
      </div>

      {/* Floating Particles */}
      <div className="fixed inset-0 pointer-events-none">
        {[...Array(50)].map((_, i) => (
          <div
            key={i}
            className="absolute w-1 h-1 bg-white rounded-full animate-pulse"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${2 + Math.random() * 3}s`
            }}
          />
        ))}
      </div>

      {/* Mouse Trail Effect */}
      <div 
        className="fixed w-4 h-4 bg-gradient-to-r from-golden-400 to-cyan-400 rounded-full pointer-events-none z-50 mix-blend-difference transition-transform duration-100 ease-out"
        style={{
          left: mousePosition.x - 8,
          top: mousePosition.y - 8,
          transform: isHovering ? 'scale(2)' : 'scale(1)'
        }}
      />

      {/* Header */}
      <header className="relative z-40 sticky top-0 py-4">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-white/95 backdrop-blur-xl rounded-2xl shadow-lg border border-white/20 px-6 py-4">
            <div className="flex justify-between items-center">
              {/* Logo */}
              <div className="flex items-center">
                <img 
                  src="/logo.png" 
                  alt="BUZZZ Logo" 
                  className="w-10 h-10 mr-3"
                />
                <span className="text-2xl font-black bg-gradient-to-r from-golden-500 to-cyan-600 bg-clip-text text-transparent tracking-tight">
                  BUZZZ
                </span>
              </div>

              {/* Navigation */}
              <nav className="hidden md:flex items-center space-x-8">
                <Link to="/" className="text-black font-bold hover:text-golden-600 transition-colors duration-200">
                  Features
                </Link>
                <Link to="/" className="text-black font-bold hover:text-golden-600 transition-colors duration-200">
                  Templates
                </Link>
                <Link to="/" className="text-black font-bold hover:text-golden-600 transition-colors duration-200">
                  Pricing
                </Link>
                <Link to="/contact" className="text-black font-bold hover:text-golden-600 transition-colors duration-200">
                  Contact
                </Link>
              </nav>

              {/* CTA Buttons */}
              <div className="flex items-center space-x-4">
                <button 
                  onClick={() => setIsAuthModalOpen(true)}
                  className="text-black font-bold hover:text-golden-600 transition-colors duration-200"
                >
                  Login
                </button>
                <button 
                  onClick={() => setIsAuthModalOpen(true)}
                  className="golden-button px-6 py-2 rounded-xl font-bold transition-all duration-200"
                >
                  Start for Free
                </button>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Hero Section */}
      <main className="relative z-30 flex items-center justify-center min-h-screen px-6 sm:px-8 py-20">
        <div className="max-w-7xl mx-auto w-full">
          <div className="text-center">
            {/* Animated Badge */}
            <div className="inline-flex items-center bg-gradient-to-r from-golden-400/20 to-cyan-400/20 backdrop-blur-xl border border-golden-400/30 rounded-full px-8 py-4 mb-12 animate-bounce">
              <span className="text-golden-400 font-black text-lg mr-2">🚀</span>
              <span className="text-white font-black text-lg">REVOLUTIONARY AI-POWERED NETWORKING</span>
              <span className="text-cyan-400 font-black text-lg ml-2">⚡</span>
            </div>

            {/* Main Heading */}
            <h1 className="text-5xl sm:text-7xl lg:text-8xl font-black text-white mb-6 leading-none tracking-tight">
              <span className="block bg-gradient-to-r from-golden-400 via-white to-cyan-400 bg-clip-text text-transparent animate-pulse">
                DIGITAL BUSINESS
              </span>
              <span className="block bg-gradient-to-r from-cyan-400 via-golden-400 to-pink-400 bg-clip-text text-transparent animate-pulse" style={{animationDelay: '0.5s'}}>
                CARDS 2.0
              </span>
            </h1>

            {/* Subtitle */}
            <p className="text-lg sm:text-xl lg:text-2xl text-white/80 mb-12 max-w-3xl mx-auto leading-relaxed font-bold">
              Experience the future of networking with 
              <span className="text-golden-400 font-black"> AI-powered analytics</span>, 
              <span className="text-cyan-400 font-black"> instant QR sharing</span>, and 
              <span className="text-pink-400 font-black"> real-time insights</span>
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-6 justify-center mb-20">
              <button
                onClick={() => setIsAuthModalOpen(true)}
                className="group golden-button px-10 py-3 rounded-2xl text-lg font-black transform hover:scale-110 hover:-translate-y-1 relative overflow-hidden"
                onMouseEnter={() => setIsHovering(true)}
                onMouseLeave={() => setIsHovering(false)}
              >
                <div className="absolute inset-0 bg-white/30 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
                <span className="relative z-10 flex items-center">
                  <span className="mr-2 text-xl">⚡</span>
                  CREATE YOUR CARD
                </span>
              </button>
              <a
                href="https://buzzz.my/@demo?preview=true"
                target="_blank"
                rel="noopener noreferrer"
                className="group border-4 border-golden-400/50 text-white px-10 py-3 rounded-2xl text-lg font-black hover:border-golden-400 hover:text-golden-400 transition-all duration-500 transform hover:scale-110 hover:-translate-y-1 backdrop-blur-xl"
                onMouseEnter={() => setIsHovering(true)}
                onMouseLeave={() => setIsHovering(false)}
              >
                <span className="flex items-center">
                  <span className="mr-2 text-xl group-hover:rotate-180 transition-transform duration-500">👁️</span>
                  VIEW DEMO
                </span>
              </a>
            </div>

            {/* Floating Stats */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto mt-12">
              {[
                { number: '10K+', label: 'Active Users', icon: '🔥', color: 'from-golden-400 to-orange-400' },
                { number: '99.9%', label: 'Uptime', icon: '⚡', color: 'from-cyan-400 to-blue-400' },
                { number: '24/7', label: 'Support', icon: '🛡️', color: 'from-pink-400 to-purple-400' }
              ].map((stat, i) => (
                <div 
                  key={i}
                  className="bg-black/40 backdrop-blur-xl border border-white/10 rounded-3xl p-8 transform hover:scale-110 transition-all duration-500 hover:border-golden-400/30"
                >
                  <div className={`w-16 h-16 bg-gradient-to-r ${stat.color} rounded-2xl flex items-center justify-center mx-auto mb-4 text-3xl animate-pulse`}>
                    {stat.icon}
                  </div>
                  <div className="text-4xl font-black text-white mb-2">{stat.number}</div>
                  <div className="text-white/60 font-bold">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </main>

      {/* Animated Background Elements */}
      <div className="fixed inset-0 pointer-events-none z-10">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-golden-400/20 to-cyan-400/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-gradient-to-r from-cyan-400/20 to-pink-400/20 rounded-full blur-3xl animate-pulse" style={{animationDelay: '1s'}}></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-r from-pink-400/20 to-golden-400/20 rounded-full blur-3xl animate-pulse" style={{animationDelay: '2s'}}></div>
      </div>

      {/* Footer */}
      <div className="relative z-40">
        <Footer />
      </div>

      {/* Auth Modal */}
      <AuthModal 
        isOpen={isAuthModalOpen} 
        onClose={() => setIsAuthModalOpen(false)} 
        initialMode="signin"
      />
    </div>
  );
}