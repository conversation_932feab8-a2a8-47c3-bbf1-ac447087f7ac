import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useSearchParams } from 'react-router-dom';
import { CreditCard, Plus, Edit3, Eye, BarChart3, Settings, Save, LogOut, Crown, Zap, QrCode, Bookmark, Sparkles, Mail, Users, Home, Palette, ShoppingBag, UserCheck, Copy, Check, Menu, X, CheckCircle, AlertCircle, Globe, Lock } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { supabase } from '../lib/supabase';
import { BusinessCard as BusinessCardType, Offer } from '../types';
import UserTypeIndicator from '../components/UserTypeIndicator';
import UpgradePrompt from '../components/UpgradePrompt';
import SubscriptionStatus from '../components/SubscriptionStatus';
import UsernameSetupModal from '../components/UsernameSetupModal';

// Import page components
import DashboardHome from '../components/dashboard/DashboardHome';
import BusinessCardPage from '../components/dashboard/BusinessCardPage';

import SavedCardsPage from '../components/dashboard/SavedCardsPage';
import SharingPage from '../components/dashboard/SharingPage';
import EcommercePage from '../components/dashboard/EcommercePage';
import AdminPage from '../components/dashboard/AdminPage';
import SettingsPage from '../components/dashboard/SettingsPage';
import CustomDomainsPage from '../components/dashboard/CustomDomainsPage';

export default function Dashboard() {
  const { user, signOut, userProfile, hasPermission, refreshUserProfile } = useAuth();
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const [businessCard, setBusinessCard] = useState<BusinessCardType | null>(null);
  const [offers, setOffers] = useState<Offer[]>([]);
  const [activePage, setActivePage] = useState<'home' | 'card' | 'saved' | 'sharing' | 'ecommerce' | 'admin' | 'settings' | 'domains'>('home');
  const [loading, setLoading] = useState(true);
  const [showUpgradePrompt, setShowUpgradePrompt] = useState(false);
  const [upgradeFeature, setUpgradeFeature] = useState('');
  const [upgradeRequiredPlan, setUpgradeRequiredPlan] = useState<'unlimited' | 'super_unlimited'>('unlimited');
  const [copiedUrl, setCopiedUrl] = useState(false);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [signingOut, setSigningOut] = useState(false);
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  const [showCanceledMessage, setShowCanceledMessage] = useState(false);
  const [showUsernameSetupModal, setShowUsernameSetupModal] = useState(false);

  useEffect(() => {
    if (user) {
      loadUserData();
    }
  }, [user]);

  useEffect(() => {
    // Always refresh user profile on dashboard load (important after Stripe checkout)
    refreshUserProfile();
  }, []);

  // Check if Gmail user needs username setup
  useEffect(() => {
    if (user && userProfile && !userProfile.username && user.app_metadata?.provider === 'google') {
      setShowUsernameSetupModal(true);
    }
  }, [user, userProfile]);

  // Handle success/canceled messages from Stripe checkout
  useEffect(() => {
    const success = searchParams.get('success');
    const canceled = searchParams.get('canceled');
    const plan = searchParams.get('plan');

    if (success === 'true') {
      setShowSuccessMessage(true);
      // Clean up URL parameters
      setSearchParams({});
      // Hide message after 5 seconds
      setTimeout(() => setShowSuccessMessage(false), 5000);
    }

    if (canceled === 'true') {
      setShowCanceledMessage(true);
      // Clean up URL parameters
      setSearchParams({});
      // Hide message after 5 seconds
      setTimeout(() => setShowCanceledMessage(false), 5000);
    }
  }, [searchParams, setSearchParams]);

  // Prevent body scroll when sidebar is open on mobile
  useEffect(() => {
    if (sidebarOpen && window.innerWidth < 1024) {
      document.body.classList.add('sidebar-open');
    } else {
      document.body.classList.remove('sidebar-open');
    }

    return () => {
      document.body.classList.remove('sidebar-open');
    };
  }, [sidebarOpen]);

  const loadUserData = async () => {
    try {
      // Load business card
      const { data: cardData } = await supabase
        .from('business_cards')
        .select('*')
        .eq('user_id', user?.id);

      if (cardData && cardData.length > 0) {
        const cardRecord = cardData[0];
        const card: BusinessCardType = {
          id: cardRecord.id,
          userId: cardRecord.user_id,
          name: cardRecord.name,
          title: cardRecord.title,
          company: cardRecord.company,
          bio: cardRecord.bio,
          email: cardRecord.email,
          phone: cardRecord.phone,
          website: cardRecord.website,
          profileImage: cardRecord.profile_image,
          backgroundImage: cardRecord.background_image,
          coverImage: cardRecord.cover_image,
          socialLinks: cardRecord.social_links || [],
          theme: cardRecord.theme || 'default',
          pageBackground: cardRecord.page_background || undefined,
          username: cardRecord.username,
          location: cardRecord.location,
          joinYear: cardRecord.join_year,
          components: cardRecord.components || []
        };
        setBusinessCard(card);

        // Load offers
        const { data: offersData } = await supabase
          .from('offers')
          .select('*')
          .eq('business_card_id', cardRecord.id)
          .order('order_index', { ascending: true });

        if (offersData) {
          const formattedOffers: Offer[] = offersData.map(offer => ({
            id: offer.id,
            title: offer.title,
            description: offer.description,
            buttonText: offer.button_text,
            landingPage: offer.landing_page,
            isActive: offer.is_active
          }));
          setOffers(formattedOffers);
        }
      } else {
        // Create default business card for new users
        await createDefaultCard();
      }
    } catch (error) {
      console.error('Error loading user data:', error);
    } finally {
      setLoading(false);
    }
  };

  const generateUniqueUsername = async (baseUsername: string): Promise<string> => {
    let username = baseUsername;
    let attempts = 0;
    const maxAttempts = 10;

    while (attempts < maxAttempts) {
      // Check if username exists (case-insensitive)
      const { data: existingCard } = await supabase
        .from('business_cards')
        .select('id')
        .filter('username', 'ilike', username)
        .limit(1);

      // If no existing card found, username is available
      if (!existingCard || existingCard.length === 0) {
        return username;
      }

      // Generate a new username variant with more robust random suffix
      attempts++;
      const randomSuffix = Math.random().toString(36).substring(2, 8);
      username = `${baseUsername}-${randomSuffix}`;
    }

    // Fallback: use timestamp if all attempts failed
    const timestamp = Date.now();
    return `${baseUsername}-${timestamp}`;
  };

  const isUniqueConstraintError = (error: any): boolean => {
    return (
      error?.code === '23505' ||
      error?.message?.includes('duplicate key value violates unique constraint') ||
      error?.message?.includes('business_cards_username_key') ||
      (error?.details && error.details.includes('Key (username)'))
    );
  };

  const createDefaultCard = async () => {
    const emailPrefix = user?.email?.split('@')[0] || 'user';
    const baseUsername = emailPrefix.toLowerCase().replace(/[^a-zA-Z0-9]/g, '');
    
    const maxRetries = 5;
    let retryCount = 0;

    while (retryCount < maxRetries) {
      try {
        // Generate a unique username
        const uniqueUsername = await generateUniqueUsername(baseUsername);

        const defaultCard = {
          user_id: user?.id,
          name: user?.email?.split('@')[0] || 'Your Name',
          title: 'Your Title',
          company: 'Your Company',
          bio: 'Tell people about yourself and what you do.',
          email: user?.email || '',
          phone: '+****************',
          website: 'https://yourwebsite.com',
          profile_image: 'https://images.pexels.com/photos/3785077/pexels-photo-3785077.jpeg?auto=compress&cs=tinysrgb&w=400',
          background_image: null,
          cover_image: null,
          social_links: [],
          theme: 'default',
          username: uniqueUsername,
          page_background: null
        };

        const { data, error } = await supabase
          .from('business_cards')
          .insert(defaultCard)
          .select()
          .single();

        if (data && !error) {
          const card: BusinessCardType = {
            id: data.id,
            userId: data.user_id,
            name: data.name,
            title: data.title,
            company: data.company,
            bio: data.bio,
            email: data.email,
            phone: data.phone,
            website: data.website,
            profileImage: data.profile_image,
            backgroundImage: data.background_image,
            coverImage: data.cover_image,
            socialLinks: data.social_links || [],
            theme: data.theme || 'default',
            pageBackground: data.page_background || undefined,
            username: data.username
          };
          setBusinessCard(card);
          return; // Success, exit the retry loop
        } else if (isUniqueConstraintError(error)) {
          // Unique constraint violation - retry with a new username
          retryCount++;
          console.log(`Username collision detected, retrying... (${retryCount}/${maxRetries})`);
          continue;
        } else {
          // Other error - log and exit
          console.error('Error creating default card:', error);
          return;
        }
      } catch (error: any) {
        if (isUniqueConstraintError(error)) {
          // Unique constraint violation - retry with a new username
          retryCount++;
          console.log(`Username collision detected, retrying... (${retryCount}/${maxRetries})`);
          continue;
        } else {
          // Other error - log and exit
          console.error('Error creating default card:', error);
          return;
        }
      }
    }

    // If we've exhausted all retries, create a username with timestamp
    try {
      const timestampUsername = `${baseUsername}-${Date.now()}`;
      
      const defaultCard = {
        user_id: user?.id,
        name: user?.email?.split('@')[0] || 'Your Name',
        title: 'Your Title',
        company: 'Your Company',
        bio: 'Tell people about yourself and what you do.',
        email: user?.email || '',
        phone: '+****************',
        website: 'https://yourwebsite.com',
        profile_image: 'https://images.pexels.com/photos/3785077/pexels-photo-3785077.jpeg?auto=compress&cs=tinysrgb&w=400',
        background_image: null,
        cover_image: null,
        social_links: [],
        theme: 'default',
        username: timestampUsername,
        page_background: null
      };

      const { data, error } = await supabase
        .from('business_cards')
        .insert(defaultCard)
        .select()
        .single();

      if (data && !error) {
        const card: BusinessCardType = {
          id: data.id,
          userId: data.user_id,
          name: data.name,
          title: data.title,
          company: data.company,
          bio: data.bio,
          email: data.email,
          phone: data.phone,
          website: data.website,
          profileImage: data.profile_image,
          backgroundImage: data.background_image,
          coverImage: data.cover_image,
          socialLinks: data.social_links || [],
          theme: data.theme || 'default',
          pageBackground: data.page_background || undefined,
          username: data.username
        };
        setBusinessCard(card);
      } else {
        console.error('Final attempt to create default card failed:', error);
      }
    } catch (finalError) {
      console.error('Final attempt to create default card failed:', finalError);
    }
  };

  const handlePageClick = (page: 'home' | 'card' | 'saved' | 'sharing' | 'ecommerce' | 'admin' | 'settings' | 'domains') => {
    if (page === 'ecommerce' && !hasPermission('ecommerce')) {
      setUpgradeFeature('E-commerce Integration');
      setUpgradeRequiredPlan('super_unlimited');
      setShowUpgradePrompt(true);
      return;
    }
    if (page === 'domains' && !hasPermission('custom_domains')) {
      setUpgradeFeature('Custom Domains');
      setUpgradeRequiredPlan('unlimited');
      setShowUpgradePrompt(true);
      return;
    }
    if (page === 'admin' && !hasPermission('admin')) {
      return; // Simply don't allow access
    }
    setActivePage(page);
    setSidebarOpen(false); // Close sidebar on mobile after selection
  };

  const handleSignOut = async () => {
    if (signingOut) return; // Prevent multiple clicks
    
    setSigningOut(true);
    
    try {
      // Clear local state first
      setBusinessCard(null);
      setOffers([]);
      
      // Sign out from Supabase (this will also clear the auth context)
      await signOut();
      
      // Small delay to ensure state is cleared
      setTimeout(() => {
        // Force navigation to home page
        window.location.replace('/');
      }, 100);
      
    } catch (error) {
      console.error('Error during sign out:', error);
      // Force navigation even if there's an error
      window.location.replace('/');
    }
  };

  const getCardUrl = () => {
    if (!businessCard?.username) return '';
    return `${window.location.origin}/@${businessCard.username}`;
  };

  const getUserDisplayName = () => {
    if (userProfile?.username) {
      return userProfile.username;
    }
    if (businessCard?.username) {
      return businessCard.username;
    }
    return user?.email?.split('@')[0] || 'User';
  };

  const handleCopyUrl = async () => {
    try {
      await navigator.clipboard.writeText(getCardUrl());
      setCopiedUrl(true);
      setTimeout(() => setCopiedUrl(false), 2000);
    } catch (error) {
      console.error('Failed to copy URL:', error);
    }
  };

  const navigationItems = [
    { id: 'home', label: 'Dashboard Home', icon: Home, description: 'Overview & Analytics' },
    { id: 'card', label: 'My Business Card', icon: CreditCard, description: 'Edit Card & Design' },
    { id: 'sharing', label: 'Sharing', icon: QrCode, description: 'QR Code & Social' },
    { id: 'saved', label: 'Saved Contacts', icon: Bookmark, description: 'Your Collection' },
    { id: 'settings', label: 'Settings', icon: Settings, description: 'Account & Security' },
    // E-commerce - Only for super admins (future feature)
    ...(hasPermission('ecommerce') ? [{ 
      id: 'ecommerce', 
      label: 'E-commerce', 
      icon: ShoppingBag, 
      description: 'Payment Settings (Admin Only)', 
      locked: true,
      adminOnly: true 
    }] : []),
    // Custom Domains - Only for super admins (future feature)
    ...(hasPermission('custom_domains') ? [{ 
      id: 'domains', 
      label: 'Custom Domains', 
      icon: Globe, 
      description: 'Domain Management (Admin Only)', 
      locked: true,
      adminOnly: true 
    }] : []),
    ...(hasPermission('admin') ? [{ id: 'admin', label: 'Admin', icon: Crown, description: 'System Management' }] : [])
  ];

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-neutral-50 via-golden-50 to-primary-50 flex items-center justify-center">
        <div className="text-center animate-fade-in">
          <div className="w-16 h-16 bg-gradient-to-br from-golden-500 to-primary-500 rounded-3xl flex items-center justify-center mx-auto mb-6 animate-bounce-gentle">
            <img 
              src="/logo.png" 
              alt="BUZZZ Logo" 
              className="w-8 h-8"
            />
          </div>
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-golden-600 mx-auto mb-4"></div>
          <p className="text-neutral-600 font-medium">Loading your dashboard...</p>
        </div>
      </div>
    );
  }

  if (!businessCard) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-neutral-50 via-golden-50 to-primary-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-neutral-600">Error loading your business card. Please try again.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-neutral-50 via-golden-50 to-primary-50">
      {/* Success/Canceled Messages */}
      {showSuccessMessage && (
        <div className="fixed top-4 right-4 z-50 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg shadow-lg animate-slide-in-right">
          <div className="flex items-center">
            <CheckCircle className="w-5 h-5 mr-2" />
            <span className="font-medium">Payment successful! Your subscription is now active.</span>
          </div>
        </div>
      )}

      {showCanceledMessage && (
        <div className="fixed top-4 right-4 z-50 bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded-lg shadow-lg animate-slide-in-right">
          <div className="flex items-center">
            <AlertCircle className="w-5 h-5 mr-2" />
            <span className="font-medium">Payment was canceled. You can try again anytime.</span>
          </div>
        </div>
      )}

      {/* Top Navigation */}
      <nav className="bg-white/80 backdrop-blur-md border-b border-neutral-200/50 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              {/* Mobile menu button */}
              <button
                onClick={() => setSidebarOpen(!sidebarOpen)}
                className="lg:hidden p-2.5 rounded-xl text-neutral-600 hover:text-primary-600 hover:bg-primary-50 transition-colors mr-3 touch-manipulation"
                aria-label={sidebarOpen ? "Close menu" : "Open menu"}
              >
                {sidebarOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
              </button>

              <Link to="/" className="flex items-center group">
                <img 
                  src="/logo.png" 
                  alt="BUZZZ Logo" 
                  className="w-9 h-9 sm:w-10 sm:h-10 mr-2 sm:mr-3 group-hover:scale-105 transition-transform duration-200"
                />
                <span className="text-lg sm:text-xl font-bold bg-gradient-to-r from-golden-600 to-primary-600 bg-clip-text text-transparent hidden sm:block">
                  Buzzz
                </span>
              </Link>
            </div>
            
            <div className="flex items-center space-x-2 sm:space-x-4 lg:space-x-6">
              {/* Username Display & Copy - Hidden on small screens */}
              {businessCard.username && (
                <div className="hidden md:flex items-center space-x-2">
                  <div className="text-right">
                    <p className="text-sm font-medium text-neutral-700">Your URL:</p>
                    <p className="text-xs text-neutral-500">buzzz.my/@{businessCard.username}</p>
                  </div>
                  <button
                    onClick={handleCopyUrl}
                    className={`p-2 rounded-lg transition-all duration-200 ${
                      copiedUrl 
                        ? 'bg-accent-100 text-accent-600' 
                        : 'bg-neutral-100 text-neutral-600 hover:bg-primary-100 hover:text-primary-600'
                    }`}
                    title="Copy URL"
                  >
                    {copiedUrl ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
                  </button>
                </div>
              )}
              
              <div className="hidden sm:flex items-center space-x-4">
                <div className="text-right">
                  <p className="text-sm font-medium text-neutral-700">Welcome back!</p>
                  <p className="text-xs text-neutral-500 truncate max-w-32">@{getUserDisplayName()}</p>
                </div>
                {userProfile && <UserTypeIndicator userType={userProfile.userType} />}
              </div>

              {/* Mobile user info */}
              <div className="sm:hidden">
                {userProfile && <UserTypeIndicator userType={userProfile.userType} />}
              </div>

              <Link 
                to={businessCard.username ? `/@${businessCard.username}?preview=true` : `/${businessCard.id}?preview=true`}
                className="flex items-center text-neutral-600 hover:text-golden-600 transition-colors font-medium p-2 rounded-lg hover:bg-golden-50"
              >
                <Eye className="w-4 h-4 sm:w-5 sm:h-5 sm:mr-2" />
                <span className="hidden sm:block">Preview</span>
              </Link>
              <button 
                onClick={handleSignOut}
                disabled={signingOut}
                className={`flex items-center text-neutral-600 hover:text-red-500 transition-colors font-medium p-2 rounded-lg hover:bg-neutral-100 ${
                  signingOut ? 'opacity-50 cursor-not-allowed' : ''
                }`}
              >
                <LogOut className="w-4 h-4 sm:w-5 sm:h-5 sm:mr-2" />
                <span className="hidden sm:block">{signingOut ? 'Signing Out...' : 'Sign Out'}</span>
              </button>
            </div>
          </div>
        </div>
      </nav>

      <div className="flex">
        {/* Sidebar Navigation - Mobile Overlay */}
        <div className={`fixed inset-0 z-40 lg:relative lg:inset-auto transition-all duration-300 ${
          sidebarOpen ? 'block' : 'hidden lg:block'
        }`}>
          {/* Mobile backdrop */}
          <div 
            className={`fixed inset-0 bg-black/50 lg:hidden transition-opacity duration-300 ${
              sidebarOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'
            }`}
            onClick={() => setSidebarOpen(false)}
          />

          {/* Sidebar */}
          <div className={`fixed left-0 top-0 h-full w-72 sm:w-80 bg-white/70 backdrop-blur-sm border-r border-neutral-200/50 p-4 sm:p-6 transform transition-transform duration-300 lg:relative lg:transform-none sidebar-mobile-scroll overflow-y-auto ${
            sidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'
          }`}>
            <div className="mb-6 sm:mb-8">
              <div className="flex items-center mb-3 sm:mb-4">
                <img 
                  src="/logo.png" 
                  alt="BUZZZ Logo" 
                  className="w-8 h-8 sm:w-10 sm:h-10 mr-3 sm:mr-4"
                />
                <h1 className="text-xl sm:text-2xl font-bold bg-gradient-to-r from-golden-900 to-neutral-700 bg-clip-text text-transparent">
                  Dashboard
                </h1>
              </div>
              <p className="text-sm sm:text-base text-neutral-600">Manage your digital presence</p>
            </div>

            {/* Subscription Status */}
            <div className="mb-4 sm:mb-6">
              <SubscriptionStatus />
            </div>

            <nav className="space-y-1 sm:space-y-2">
              {navigationItems.map((item) => (
                <button
                  key={item.id}
                  onClick={() => handlePageClick(item.id as any)}
                  className={`w-full text-left p-3 sm:p-4 rounded-xl sm:rounded-2xl transition-all duration-200 group mobile-nav-item ${
                    activePage === item.id
                      ? 'bg-gradient-to-r from-golden-500 to-primary-600 text-white shadow-colored'
                      : item.locked
                      ? 'bg-red-50 border border-red-200 hover:bg-red-100 text-red-700'
                      : 'hover:bg-golden-50 text-neutral-700'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center min-w-0 flex-1">
                      <item.icon className={`w-4 h-4 sm:w-5 sm:h-5 mr-2 sm:mr-3 flex-shrink-0 ${
                        activePage === item.id ? 'text-white' : item.locked ? 'text-red-500' : 'text-neutral-500 group-hover:text-golden-500'
                      }`} />
                      <div className="min-w-0 flex-1">
                        <div className={`font-semibold text-sm sm:text-base truncate ${activePage === item.id ? 'text-white' : item.locked ? 'text-red-800' : 'text-neutral-900'}`}>
                          {item.label}
                        </div>
                        <div className={`text-xs sm:text-sm truncate ${activePage === item.id ? 'text-white/80' : item.locked ? 'text-red-600' : 'text-neutral-500 group-hover:text-golden-600'}`}>
                          {item.description}
                        </div>
                      </div>
                    </div>
                    {item.locked && (
                      <Lock className="w-4 h-4 sm:w-5 sm:h-5 text-red-500 flex-shrink-0" />
                    )}
                    {item.adminOnly && (
                      <Crown className="w-4 h-4 sm:w-5 sm:h-5 text-yellow-500 flex-shrink-0" />
                    )}
                  </div>
                </button>
              ))}
            </nav>

            {/* Mobile URL copy section */}
            {businessCard.username && (
              <div className="mt-6 sm:mt-8 p-3 sm:p-4 bg-gradient-to-r from-primary-50 to-secondary-50 rounded-xl sm:rounded-2xl border border-primary-200 lg:hidden">
                <div className="text-center">
                  <p className="text-xs sm:text-sm font-medium text-neutral-700 mb-2">Your URL</p>
                  <div className="flex items-center justify-center space-x-2">
                    <code className="text-xs bg-white px-2 py-1 rounded text-primary-600 font-mono break-all">
                      buzzz.my/@{businessCard.username}
                    </code>
                    <button
                      onClick={handleCopyUrl}
                      className={`p-1 rounded transition-all duration-200 flex-shrink-0 ${
                        copiedUrl 
                          ? 'bg-accent-100 text-accent-600' 
                          : 'bg-white text-neutral-600 hover:bg-primary-100 hover:text-primary-600'
                      }`}
                    >
                      {copiedUrl ? <Check className="w-3 h-3" /> : <Copy className="w-3 h-3" />}
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 p-4 sm:p-6 lg:p-8">
          <div className="animate-fade-in">
            {activePage === 'home' && (
              <DashboardHome 
                businessCard={businessCard} 
                offers={offers}
                onEditCard={() => setActivePage('card')}
                onUpgradeClick={() => {
                  setUpgradeFeature('Analytics Dashboard');
                  setUpgradeRequiredPlan('unlimited');
                  setShowUpgradePrompt(true);
                }}
              />
            )}
            {activePage === 'card' && (
              <BusinessCardPage 
                businessCard={businessCard}
                offers={offers}
                onBusinessCardUpdate={setBusinessCard}
                onOffersUpdate={setOffers}
                hasPermission={hasPermission}
                onUpgradeClick={() => {
                  setUpgradeFeature('Premium Themes & Backgrounds');
                  setUpgradeRequiredPlan('unlimited');
                  setShowUpgradePrompt(true);
                }}
              />
            )}

            {activePage === 'saved' && <SavedCardsPage />}
            {activePage === 'sharing' && (
              <SharingPage 
                businessCard={businessCard}
              />
            )}
            {activePage === 'ecommerce' && hasPermission('ecommerce') && (
              <EcommercePage 
                onUpgradeClick={() => {
                  setUpgradeFeature('E-commerce Integration');
                  setUpgradeRequiredPlan('super_unlimited');
                  setShowUpgradePrompt(true);
                }}
              />
            )}
            {activePage === 'ecommerce' && !hasPermission('ecommerce') && (
              <div className="text-center py-12">
                <Lock className="w-16 h-16 text-red-500 mx-auto mb-4" />
                <h2 className="text-2xl font-bold text-gray-900 mb-2">Feature Locked</h2>
                <p className="text-gray-600 mb-4">E-commerce integration is only available to super administrators.</p>
                <p className="text-sm text-gray-500">This feature is reserved for future updates and system administration.</p>
              </div>
            )}
            {activePage === 'settings' && <SettingsPage />}
            {activePage === 'domains' && hasPermission('custom_domains') && (
              <CustomDomainsPage 
                businessCard={businessCard}
              />
            )}
            {activePage === 'domains' && !hasPermission('custom_domains') && (
              <div className="text-center py-12">
                <Lock className="w-16 h-16 text-red-500 mx-auto mb-4" />
                <h2 className="text-2xl font-bold text-gray-900 mb-2">Feature Locked</h2>
                <p className="text-gray-600 mb-4">Custom domain management is only available to super administrators.</p>
                <p className="text-sm text-gray-500">This feature is reserved for future updates and system administration.</p>
              </div>
            )}
            {activePage === 'admin' && hasPermission('admin') && <AdminPage />}
          </div>
        </div>
      </div>

      {/* Upgrade Prompt Modal */}
      {showUpgradePrompt && (
        <UpgradePrompt
          feature={upgradeFeature}
          requiredPlan={upgradeRequiredPlan}
          onClose={() => setShowUpgradePrompt(false)}
        />
      )}

      {/* Username Setup Modal */}
      {showUsernameSetupModal && (
        <UsernameSetupModal
          isOpen={showUsernameSetupModal}
          onClose={() => setShowUsernameSetupModal(false)}
          onComplete={() => {
            // Reload user data to reflect the new username
            loadUserData();
            setShowUsernameSetupModal(false);
          }}
          userEmail={user?.email}
          userName={user?.user_metadata?.full_name || user?.user_metadata?.name}
        />
      )}
    </div>
  );
}