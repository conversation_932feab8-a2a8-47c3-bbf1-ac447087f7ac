import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Zap } from 'lucide-react';
import Footer from '../components/Footer';
import { useAuth } from '../contexts/AuthContext';

export default function PrivacyPolicy() {
  const { user } = useAuth();
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isHovering, setIsHovering] = useState(false);

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };
    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  return (
    <div className="min-h-screen relative overflow-hidden bg-black">
      {/* Animated Background */}
      <div className="fixed inset-0 bg-gradient-to-br from-purple-900 via-blue-900 to-pink-900">
        <div className="absolute inset-0 opacity-30">
          <div className="absolute inset-0" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
          }}></div>
        </div>
      </div>

      {/* Floating Particles */}
      <div className="fixed inset-0 pointer-events-none">
        {[...Array(50)].map((_, i) => (
          <div
            key={i}
            className="absolute w-1 h-1 bg-white rounded-full animate-pulse"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${2 + Math.random() * 3}s`
            }}
          />
        ))}
      </div>

      {/* Mouse Trail Effect */}
      <div 
        className="fixed w-4 h-4 bg-gradient-to-r from-cyan-400 to-pink-400 rounded-full pointer-events-none z-50 mix-blend-difference transition-transform duration-100 ease-out"
        style={{
          left: mousePosition.x - 8,
          top: mousePosition.y - 8,
          transform: isHovering ? 'scale(2)' : 'scale(1)'
        }}
      />

      {/* Animated Background Elements */}
      <div className="fixed inset-0 pointer-events-none z-10">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-cyan-400/20 to-pink-400/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-gradient-to-r from-pink-400/20 to-purple-400/20 rounded-full blur-3xl animate-pulse" style={{animationDelay: '1s'}}></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-r from-purple-400/20 to-cyan-400/20 rounded-full blur-3xl animate-pulse" style={{animationDelay: '2s'}}></div>
      </div>

      <nav className="relative z-40 bg-white/95 backdrop-blur-xl border-b border-white/20 sticky top-0">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link to="/" className="flex items-center group">
              <div className="w-10 h-10 bg-gradient-to-br from-cyan-500 to-pink-500 rounded-xl flex items-center justify-center mr-3 shadow-lg group-hover:scale-105 transition-transform duration-200">
                <div className="relative w-6 h-6">
                  {/* B letter with modern design */}
                  <div className="absolute inset-0 bg-white rounded-sm"></div>
                  <div className="absolute top-0 left-0 w-1 h-6 bg-cyan-500 rounded-l-sm"></div>
                  <div className="absolute top-1 left-1 w-4 h-1 bg-cyan-500 rounded-sm"></div>
                  <div className="absolute top-2.5 left-1 w-3 h-1 bg-pink-500 rounded-sm"></div>
                  <div className="absolute bottom-1 left-1 w-4 h-1 bg-pink-500 rounded-sm"></div>
                  <div className="absolute top-1 right-1 w-1 h-4 bg-pink-500 rounded-sm"></div>
                </div>
              </div>
              <span className="text-2xl font-black bg-gradient-to-r from-cyan-600 to-pink-600 bg-clip-text text-transparent tracking-tight">
                BUZZZ
              </span>
            </Link>
            <div>
              {user ? (
                <Link 
                  to="/dashboard" 
                  className="bg-gradient-to-r from-primary-500 to-primary-600 text-white px-4 py-2 rounded-2xl hover:from-primary-600 hover:to-primary-700 transition-all duration-200 font-medium shadow-colored transform hover:scale-105 text-sm"
                >
                  Dashboard
                </Link>
              ) : (
                <Link
                  to="/"
                  className="bg-gradient-to-r from-primary-500 to-primary-600 text-white px-4 py-2 rounded-2xl hover:from-primary-600 hover:to-primary-700 transition-all duration-200 font-medium shadow-colored transform hover:scale-105 text-sm"
                >
                  Sign In
                </Link>
              )}
            </div>
          </div>
        </div>
      </nav>
      <div className="relative z-30 min-h-screen flex flex-col items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-2xl w-full bg-white/10 backdrop-blur-xl rounded-2xl shadow-xl p-8 md:p-12 border border-white/20">
          <h1 className="text-3xl font-bold text-white mb-4">Privacy Policy</h1>
          <div className="prose prose-invert max-w-none">
            <h2 className="text-white">Privacy Policy for Buzzz.my</h2>
            <p className="text-white/80"><strong>Effective Date:</strong> 21 June 2025</p>
            <p className="text-white/80">At Buzzz.my, we respect your privacy and are committed to protecting your personal information. This policy explains how we collect, use, and share information when you use our platform to create and share your digital business card or bio link page.</p>
            <h3 className="text-white">1. Information We Collect</h3>
            <p className="text-white/80"><strong>a. Personal Information</strong><br/>
            - Name, email address, phone number (optional)<br/>
            - Profile photo, business title, social media links, website URL, etc.<br/>
            - Any additional content you add to your digital business card or link page</p>
            <p className="text-white/80"><strong>b. Technical Information</strong><br/>
            - Browser type, IP address, device info<br/>
            - Usage data such as page views and interaction statistics</p>
            <p className="text-white/80"><strong>c. Cookies & Tracking</strong><br/>
            We use cookies and similar technologies to enhance your experience, remember preferences, and measure performance.</p>
            <h3 className="text-white">2. How We Use Your Information</h3>
            <ul className="text-white/80">
              <li>- Create and display your digital business card or link page</li>
              <li>- Allow you to manage and share your profile publicly</li>
              <li>- Provide customer support and technical assistance</li>
              <li>- Send updates or marketing emails (only with consent)</li>
              <li>- Improve and secure our services</li>
            </ul>
            <h3 className="text-white">3. Public Visibility</h3>
            <p className="text-white/80">You control what is shown on your public profile. Information you add (e.g., links, contact info) will be publicly accessible via your Buzzz.my page unless marked private (if such option exists).</p>
            <h3 className="text-white">4. Sharing of Information</h3>
            <p className="text-white/80">We do not sell your data. We may share your information with:</p>
            <ul className="text-white/80">
              <li>- Trusted service providers (e.g., for hosting, analytics)</li>
              <li>- Legal authorities when required by law</li>
            </ul>
            <h3 className="text-white">5. Data Security</h3>
            <p className="text-white/80">We use industry-standard measures to protect your information. However, no system is fully secure — use caution when sharing personal contact details on public pages.</p>
            <h3 className="text-white">6. Your Rights</h3>
            <ul className="text-white/80">
              <li>- Access, edit, or delete your personal data at any time</li>
              <li>- Request data deletion by emailing us</li>
              <li>- Opt-out of marketing communications</li>
            </ul>
            <h3 className="text-white">7. Third-Party Links</h3>
            <p className="text-white/80">Your page may contain links to external sites (e.g., social media). We're not responsible for their privacy practices.</p>
            <h3 className="text-white">8. Children's Privacy</h3>
            <p className="text-white/80">Buzzz.my is not intended for users under 13 years old. We do not knowingly collect information from children.</p>
            <h3 className="text-white">9. Changes to This Policy</h3>
            <p className="text-white/80">We may update this Privacy Policy. If we do, we'll notify users via email or update the effective date on this page.</p>
            <h3 className="text-white">10. Contact Us</h3>
            <p className="text-white/80">If you have questions or requests regarding your data, please contact us at:</p>
            <ul className="text-white/80">
              <li>📧 Email: <a href="mailto:<EMAIL>" className="text-cyan-400 hover:underline"><EMAIL></a></li>
              <li>🌐 Website: <a href="https://buzzz.my" target="_blank" rel="noopener noreferrer" className="text-cyan-400 hover:underline">https://buzzz.my</a></li>
            </ul>
          </div>
        </div>
      </div>
      <div className="relative z-40">
        <Footer />
      </div>
    </div>
  );
} 