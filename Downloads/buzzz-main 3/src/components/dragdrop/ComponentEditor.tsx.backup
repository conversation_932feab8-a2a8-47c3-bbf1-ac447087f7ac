import React, { useState } from 'react';
import { X, Save, User, FileText, Mail, Share2, Gift, MapPin, Globe, Star } from 'lucide-react';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import CoverImageUpload from '../CoverImageUpload';
import SocialLinksEditor from '../SocialLinksEditor';
import { CardComponent } from './types';
import { BusinessCard } from '../../types';

interface ComponentEditorProps {
  component: CardComponent;
  businessCard: BusinessCard;
  onClose: () => void;
  onSave: (updatedComponent: CardComponent) => void;
  onBusinessCardUpdate?: (updatedCard: BusinessCard) => void;
}

const iconMap = {
  User,
  FileText,
  Mail,
  Share2,
  Gift,
  MapPin,
  Globe,
  Star,
};

export default function ComponentEditor({ 
  component, 
  businessCard, 
  onClose, 
  onSave,
  onBusinessCardUpdate 
}: ComponentEditorProps) {
  const [editedConfig, setEditedConfig] = useState(component.config ? JSON.parse(JSON.stringify(component.config)) : {});
  const [customContent, setCustomContent] = useState(component.config?.content || '');
  const [customTitle, setCustomTitle] = useState(component.config?.title || '');

  const IconComponent = iconMap[component.icon as keyof typeof iconMap];

  // Quill editor configuration
  const quillModules = {
    toolbar: [
      [{ 'header': [1, 2, 3, false] }],
      ['bold', 'italic', 'underline', 'strike'],
      [{ 'color': [] }, { 'background': [] }],
      [{ 'list': 'ordered'}, { 'list': 'bullet' }],
      [{ 'align': [] }],
      ['link', 'image'],
      ['blockquote', 'code-block'],
      [{ 'indent': '-1'}, { 'indent': '+1' }],
      ['clean']
    ],
  };

  const quillFormats = [
    'header', 'bold', 'italic', 'underline', 'strike',
    'color', 'background', 'list', 'bullet', 'align',
    'link', 'image', 'blockquote', 'code-block', 'indent'
  ];

  const handleSave = () => {
    const updatedComponent: CardComponent = {
      ...component,
      config: {
        ...editedConfig,
        content: customContent,
        title: customTitle,
      }
    };
    
    // Update business card data if any business card fields were edited
    if (onBusinessCardUpdate) {
      const updatedBusinessCard: BusinessCard = { ...businessCard };
      
      // Update business card fields if they exist in the config
      if (editedConfig.name !== undefined) updatedBusinessCard.name = editedConfig.name;
      if (editedConfig.jobTitle !== undefined) updatedBusinessCard.title = editedConfig.jobTitle;
      if (editedConfig.company !== undefined) updatedBusinessCard.company = editedConfig.company;
      if (editedConfig.bio !== undefined) updatedBusinessCard.bio = editedConfig.bio;
      if (editedConfig.email !== undefined) updatedBusinessCard.email = editedConfig.email;
      if (editedConfig.phone !== undefined) updatedBusinessCard.phone = editedConfig.phone;
      if (editedConfig.website !== undefined) updatedBusinessCard.website = editedConfig.website;
      if (editedConfig.location !== undefined) updatedBusinessCard.location = editedConfig.location;
      if (editedConfig.joinYear !== undefined) updatedBusinessCard.joinYear = editedConfig.joinYear;
      if (editedConfig.socialLinks !== undefined) updatedBusinessCard.socialLinks = editedConfig.socialLinks;
      if (editedConfig.profileImage !== undefined) updatedBusinessCard.profileImage = editedConfig.profileImage;
      
      onBusinessCardUpdate(updatedBusinessCard);
    }
    
    onSave(updatedComponent);
    onClose();
  };

  const renderProfileEditor = () => (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Profile Settings</h3>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Profile Picture
        </label>
        <CoverImageUpload
          currentImage={editedConfig.profileImage || businessCard.profileImage || ''}
          onImageChange={(img) => setEditedConfig(prev => ({ ...prev, profileImage: img || '' }))}
          canUpload={true}
          className="mb-4"
        />
      </div>

      <div className="border-t border-gray-200 pt-4">
        <h4 className="text-md font-semibold text-gray-800 mb-4">Edit Profile Information</h4>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Full Name
            </label>
            <input
              type="text"
              value={editedConfig.name || businessCard.name || ''}
              onChange={(e) => setEditedConfig(prev => ({ ...prev, name: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              placeholder="Enter your full name"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Job Title
            </label>
            <input
              type="text"
              value={editedConfig.jobTitle || businessCard.title || ''}
              onChange={(e) => setEditedConfig(prev => ({ ...prev, jobTitle: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              placeholder="Enter your job title"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Company
            </label>
            <input
              type="text"
              value={editedConfig.company || businessCard.company || ''}
              onChange={(e) => setEditedConfig(prev => ({ ...prev, company: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              placeholder="Enter your company"
            />
          </div>
        </div>
      </div>
      
      <div className="grid grid-cols-2 gap-4">
        <div className="flex items-center">
          <input
            type="checkbox"
            id="showAvatar"
            checked={editedConfig.showAvatar !== false}
            onChange={(e) => setEditedConfig(prev => ({ ...prev, showAvatar: e.target.checked }))}
            className="mr-2"
          />
          <label htmlFor="showAvatar" className="text-sm text-gray-700">Show Avatar</label>
        </div>
        
        <div className="flex items-center">
          <input
            type="checkbox"
            id="showName"
            checked={editedConfig.showName !== false}
            onChange={(e) => setEditedConfig(prev => ({ ...prev, showName: e.target.checked }))}
            className="mr-2"
          />
          <label htmlFor="showName" className="text-sm text-gray-700">Show Name</label>
        </div>
        
        <div className="flex items-center">
          <input
            type="checkbox"
            id="showTitle"
            checked={editedConfig.showTitle !== false}
            onChange={(e) => setEditedConfig(prev => ({ ...prev, showTitle: e.target.checked }))}
            className="mr-2"
          />
          <label htmlFor="showTitle" className="text-sm text-gray-700">Show Title</label>
        </div>
        
        <div className="flex items-center">
          <input
            type="checkbox"
            id="showCompany"
            checked={editedConfig.showCompany !== false}
            onChange={(e) => setEditedConfig(prev => ({ ...prev, showCompany: e.target.checked }))}
            className="mr-2"
          />
          <label htmlFor="showCompany" className="text-sm text-gray-700">Show Company</label>
        </div>
      </div>
    </div>
  );

  const renderBioEditor = () => (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Bio Settings</h3>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Bio Text
        </label>
        <textarea
          value={editedConfig.bio || businessCard.bio || ''}
          onChange={(e) => setEditedConfig(prev => ({ ...prev, bio: e.target.value }))}
          rows={4}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none"
          placeholder="Tell people about yourself..."
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Maximum Length
        </label>
        <input
          type="number"
          value={editedConfig.maxLength || 200}
          onChange={(e) => setEditedConfig(prev => ({ ...prev, maxLength: parseInt(e.target.value) }))}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          min="50"
          max="500"
        />
      </div>
      
      <div className="flex items-center">
        <input
          type="checkbox"
          id="showReadMore"
          checked={editedConfig.showReadMore !== false}
          onChange={(e) => setEditedConfig(prev => ({ ...prev, showReadMore: e.target.checked }))}
          className="mr-2"
        />
        <label htmlFor="showReadMore" className="text-sm text-gray-700">Show "Read More" button</label>
      </div>
    </div>
  );

  const renderContactEditor = () => (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Contact Settings</h3>
      
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Email Address
          </label>
          <input
            type="email"
            value={editedConfig.email || businessCard.email || ''}
            onChange={(e) => setEditedConfig(prev => ({ ...prev, email: e.target.value }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            placeholder="<EMAIL>"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Phone Number
          </label>
          <input
            type="tel"
            value={editedConfig.phone || businessCard.phone || ''}
            onChange={(e) => setEditedConfig(prev => ({ ...prev, phone: e.target.value }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            placeholder="+1234567890"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Website
          </label>
          <input
            type="url"
            value={editedConfig.website || businessCard.website || ''}
            onChange={(e) => setEditedConfig(prev => ({ ...prev, website: e.target.value }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            placeholder="https://yourwebsite.com"
          />
        </div>
      </div>
      
      <div className="border-t border-gray-200 pt-4">
        <h4 className="text-md font-semibold text-gray-800 mb-3">Display Options</h4>
        <div className="space-y-3">
        <div className="flex items-center">
          <input
            type="checkbox"
            id="showEmail"
            checked={editedConfig.showEmail !== false}
            onChange={(e) => setEditedConfig(prev => ({ ...prev, showEmail: e.target.checked }))}
            className="mr-2"
          />
          <label htmlFor="showEmail" className="text-sm text-gray-700">Show Email</label>
        </div>
        
        <div className="flex items-center">
          <input
            type="checkbox"
            id="showPhone"
            checked={editedConfig.showPhone !== false}
            onChange={(e) => setEditedConfig(prev => ({ ...prev, showPhone: e.target.checked }))}
            className="mr-2"
          />
          <label htmlFor="showPhone" className="text-sm text-gray-700">Show Phone</label>
        </div>
        
        <div className="flex items-center">
          <input
            type="checkbox"
            id="showWebsite"
            checked={editedConfig.showWebsite !== false}
            onChange={(e) => setEditedConfig(prev => ({ ...prev, showWebsite: e.target.checked }))}
            className="mr-2"
          />
          <label htmlFor="showWebsite" className="text-sm text-gray-700">Show Website</label>
        </div>
      </div>
    </div>
  );

  const renderSocialEditor = () => (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Social Media Settings</h3>
      
      <div>
        <SocialLinksEditor
          socialLinks={editedConfig.socialLinks || businessCard.socialLinks || []}
          onSocialLinksChange={(links) => setEditedConfig(prev => ({ ...prev, socialLinks: links }))}
        />
      </div>
      
      <div className="border-t border-gray-200 pt-4">
        <h4 className="text-md font-semibold text-gray-800 mb-3">Layout Options</h4>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Layout
          </label>
          <select
            value={editedConfig.layout || 'grid'}
            onChange={(e) => setEditedConfig(prev => ({ ...prev, layout: e.target.value }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          >
            <option value="grid">Grid Layout</option>
            <option value="horizontal">Horizontal Layout</option>
          </select>
        </div>
      </div>
    </div>
  );

  const renderOffersEditor = () => (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Special Offer Settings</h3>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Section Title
        </label>
        <input
          type="text"
          value={editedConfig.title || 'Special Offer'}
          onChange={(e) => setEditedConfig(prev => ({ ...prev, title: e.target.value }))}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          placeholder="Special Offer"
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Section Subtitle
        </label>
        <input
          type="text"
          value={editedConfig.subtitle || 'Limited time offer'}
          onChange={(e) => setEditedConfig(prev => ({ ...prev, subtitle: e.target.value }))}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          placeholder="Limited time offer"
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Offer Title
        </label>
        <input
          type="text"
          value={editedConfig.offerTitle || '20% Off First Order'}
          onChange={(e) => setEditedConfig(prev => ({ ...prev, offerTitle: e.target.value }))}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          placeholder="Enter offer title"
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Offer Description
        </label>
        <textarea
          value={editedConfig.offerDescription || 'Get 20% off your first purchase with us'}
          onChange={(e) => setEditedConfig(prev => ({ ...prev, offerDescription: e.target.value }))}
          rows={3}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none"
          placeholder="Enter offer description"
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Button Text
        </label>
        <input
          type="text"
          value={editedConfig.offerButtonText || 'Claim Offer'}
          onChange={(e) => setEditedConfig(prev => ({ ...prev, offerButtonText: e.target.value }))}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          placeholder="Enter button text"
        />
      </div>
      
      <div className="border-2 border-dashed border-blue-300 rounded-lg p-4 bg-blue-50/30 mt-6">
        <h4 className="text-md font-semibold text-blue-900 mb-4 flex items-center">
          <svg className="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          Landing Page Settings
        </h4>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Header Photo
          </label>
          <CoverImageUpload
            currentImage={editedConfig.landingPageHeaderImage || ''}
            onImageChange={(img) => setEditedConfig(prev => ({ ...prev, landingPageHeaderImage: img || '' }))}
            canUpload={true}
            className="mb-4"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Landing Page Title
          </label>
          <input
            type="text"
            value={editedConfig.landingPageTitle || 'Special Offer Details'}
            onChange={(e) => setEditedConfig(prev => ({ ...prev, landingPageTitle: e.target.value }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            placeholder="Landing page title"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Landing Page Subtitle
          </label>
          <input
            type="text"
            value={editedConfig.landingPageSubtitle || 'Limited time offer'}
            onChange={(e) => setEditedConfig(prev => ({ ...prev, landingPageSubtitle: e.target.value }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            placeholder="Landing page subtitle"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Landing Page Content
          </label>
          <div className="border border-gray-300 rounded-lg overflow-hidden">
            <ReactQuill
              theme="snow"
              value={editedConfig.landingPageContent || 'Add your offer details here...'}
              onChange={(content) => setEditedConfig(prev => ({ ...prev, landingPageContent: content }))}
              modules={quillModules}
              formats={quillFormats}
              placeholder="Write your landing page content here. You can format text, add images, links, and more..."
              style={{ minHeight: '150px' }}
            />
          </div>
          <p className="text-xs text-gray-500 mt-1">
            Use the toolbar to format text, add images, links, and create rich content for your landing page.
          </p>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            CTA Button Text
          </label>
          <input
            type="text"
            value={editedConfig.landingPageCtaText || 'Get Started'}
            onChange={(e) => setEditedConfig(prev => ({ ...prev, landingPageCtaText: e.target.value }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            placeholder="CTA button text"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            CTA Button URL
          </label>
          <input
            type="url"
            value={editedConfig.landingPageCtaUrl || 'https://example.com/signup'}
            onChange={(e) => setEditedConfig(prev => ({ ...prev, landingPageCtaUrl: e.target.value }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            placeholder="https://example.com/signup"
          />
        </div>
        
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Background Color
            </label>
            <input
              type="color"
              value={editedConfig.landingPageBgColor || '#ffffff'}
              onChange={(e) => setEditedConfig(prev => ({ ...prev, landingPageBgColor: e.target.value }))}
              className="w-full h-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Text Color
            </label>
            <input
              type="color"
              value={editedConfig.landingPageTextColor || '#000000'}
              onChange={(e) => setEditedConfig(prev => ({ ...prev, landingPageTextColor: e.target.value }))}
              className="w-full h-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            />
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              CTA Button Color
            </label>
            <input
              type="color"
              value={editedConfig.landingPageCtaButtonColor || '#2563eb'}
              onChange={(e) => setEditedConfig(prev => ({ ...prev, landingPageCtaButtonColor: e.target.value }))}
              className="w-full h-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              CTA Text Color
            </label>
            <input
              type="color"
              value={editedConfig.landingPageCtaTextColor || '#ffffff'}
              onChange={(e) => setEditedConfig(prev => ({ ...prev, landingPageCtaTextColor: e.target.value }))}
              className="w-full h-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            />
          </div>
        </div>
      </div>
    </div>
  );



  const renderLocationEditor = () => (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Location Settings</h3>
      
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Location
          </label>
          <input
            type="text"
            value={editedConfig.location || businessCard.location || ''}
            onChange={(e) => setEditedConfig(prev => ({ ...prev, location: e.target.value }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            placeholder="Remote, New York, etc."
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Join Year
          </label>
          <input
            type="number"
            min="1900"
            max={new Date().getFullYear()}
            value={editedConfig.joinYear || businessCard.joinYear || ''}
            onChange={(e) => setEditedConfig(prev => ({ ...prev, joinYear: e.target.value ? parseInt(e.target.value) : undefined }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            placeholder="2020"
          />
        </div>
      </div>
      
      <div className="border-t border-gray-200 pt-4">
        <h4 className="text-md font-semibold text-gray-800 mb-3">Display Options</h4>
        <div className="space-y-3">
        <div className="flex items-center">
          <input
            type="checkbox"
            id="showLocation"
            checked={editedConfig.showLocation !== false}
            onChange={(e) => setEditedConfig(prev => ({ ...prev, showLocation: e.target.checked }))}
            className="mr-2"
          />
          <label htmlFor="showLocation" className="text-sm text-gray-700">Show Location</label>
        </div>
        
        <div className="flex items-center">
          <input
            type="checkbox"
            id="showJoinYear"
            checked={editedConfig.showJoinYear !== false}
            onChange={(e) => setEditedConfig(prev => ({ ...prev, showJoinYear: e.target.checked }))}
            className="mr-2"
          />
          <label htmlFor="showJoinYear" className="text-sm text-gray-700">Show Join Year</label>
        </div>
      </div>
    </div>
  );

  const renderCustomEditor = () => (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Custom Content</h3>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Section Title
        </label>
        <input
          type="text"
          value={customTitle}
          onChange={(e) => setCustomTitle(e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          placeholder="Enter section title"
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Content
        </label>
        <textarea
          value={customContent}
          onChange={(e) => setCustomContent(e.target.value)}
          rows={4}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none"
          placeholder="Enter your content here..."
        />
      </div>
      
      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Background Color
          </label>
          <input
            type="color"
            value={editedConfig.backgroundColor || '#ffffff'}
            onChange={(e) => setEditedConfig(prev => ({ ...prev, backgroundColor: e.target.value }))}
            className="w-full h-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Text Color
          </label>
          <input
            type="color"
            value={editedConfig.textColor || '#000000'}
            onChange={(e) => setEditedConfig(prev => ({ ...prev, textColor: e.target.value }))}
            className="w-full h-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          />
        </div>
      </div>
    </div>
  );


  const renderEditorContent = () => {
    switch (component.type) {
      case 'profile':
        return renderProfileEditor();
      case 'bio':
        return renderBioEditor();
      case 'contact':
        return renderContactEditor();
      case 'social':
        return renderSocialEditor();
      case 'offers':
        return renderOffersEditor();
      case 'location':
        return renderLocationEditor();
      case 'custom':
        return renderCustomEditor();
      default:
        return <div className="text-gray-500">No editor available for this component type.</div>;
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center">
            {IconComponent && <IconComponent className="w-6 h-6 text-primary-500 mr-3" />}
            <div>
              <h2 className="text-xl font-bold text-gray-900">{component.title}</h2>
              <p className="text-sm text-gray-500">{component.description}</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {renderEditorContent()}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors font-medium"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            className="flex items-center bg-gradient-to-r from-primary-500 to-primary-600 text-white px-6 py-2 rounded-xl hover:from-primary-600 hover:to-primary-700 transition-all duration-200 font-medium shadow-colored transform hover:scale-105"
          >
            <Save className="w-4 h-4 mr-2" />
            Save Changes
          </button>
        </div>
      </div>
    </div>
  );
}
