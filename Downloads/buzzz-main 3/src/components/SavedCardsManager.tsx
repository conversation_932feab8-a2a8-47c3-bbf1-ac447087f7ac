import React, { useState, useEffect } from 'react';
import { Search, Filter, Eye, Download, Trash2, Plus, Tag, Calendar, User, Building, Mail, Phone, Globe, MessageSquare } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { supabase } from '../lib/supabase';
import { SavedCard, BusinessCard } from '../types';
import { downloadVCard } from '../utils/vcard';

export default function SavedCardsManager() {
  const { user } = useAuth();
  const [savedCards, setSavedCards] = useState<SavedCard[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTag, setSelectedTag] = useState<string>('all');
  const [sortBy, setSortBy] = useState<'name' | 'company' | 'saved_date'>('saved_date');
  const [showAddNoteModal, setShowAddNoteModal] = useState(false);
  const [selectedCard, setSelectedCard] = useState<SavedCard | null>(null);
  const [noteText, setNoteText] = useState('');
  const [tags, setTags] = useState<string[]>([]);

  useEffect(() => {
    if (user) {
      loadSavedCards();
    }
  }, [user]);

  const loadSavedCards = async () => {
    try {
      const { data, error } = await supabase
        .from('saved_cards')
        .select('*')
        .eq('user_id', user?.id)
        .order('saved_at', { ascending: false });

      if (data && !error) {
        const formattedCards: SavedCard[] = data.map(card => ({
          id: card.id,
          userId: card.user_id,
          savedCardData: card.saved_card_data,
          savedAt: card.saved_at,
          notes: card.notes,
          tags: card.tags || []
        }));
        setSavedCards(formattedCards);

        // Extract unique tags
        const allTags = formattedCards.flatMap(card => card.tags || []);
        const uniqueTags = Array.from(new Set(allTags));
        setTags(uniqueTags);
      }
    } catch (error) {
              console.error('Error loading saved contacts:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteCard = async (cardId: string) => {
    if (!confirm('Are you sure you want to remove this saved card?')) return;

    try {
      const { error } = await supabase
        .from('saved_cards')
        .delete()
        .eq('id', cardId);

      if (!error) {
        setSavedCards(savedCards.filter(card => card.id !== cardId));
      }
    } catch (error) {
      console.error('Error deleting saved card:', error);
    }
  };

  const handleAddNote = async () => {
    if (!selectedCard) return;

    try {
      const { error } = await supabase
        .from('saved_cards')
        .update({ 
          notes: noteText,
          updated_at: new Date().toISOString()
        })
        .eq('id', selectedCard.id);

      if (!error) {
        setSavedCards(savedCards.map(card => 
          card.id === selectedCard.id 
            ? { ...card, notes: noteText }
            : card
        ));
        setShowAddNoteModal(false);
        setNoteText('');
        setSelectedCard(null);
      }
    } catch (error) {
      console.error('Error updating note:', error);
    }
  };

  const handleDownloadVCard = (card: SavedCard) => {
    downloadVCard(card.savedCardData);
  };

  const handleViewCard = (card: SavedCard) => {
    window.open(`/${card.savedCardData.id}`, '_blank');
  };

  const filteredCards = savedCards.filter(card => {
    const searchableText = `${card.savedCardData.name} ${card.savedCardData.company} ${card.savedCardData.title} ${card.savedCardData.email}`.toLowerCase();
    const matchesSearch = searchableText.includes(searchTerm.toLowerCase());
    const matchesTag = selectedTag === 'all' || (card.tags && card.tags.includes(selectedTag));
    return matchesSearch && matchesTag;
  });

  const sortedCards = [...filteredCards].sort((a, b) => {
    switch (sortBy) {
      case 'name':
        return a.savedCardData.name.localeCompare(b.savedCardData.name);
      case 'company':
        return a.savedCardData.company.localeCompare(b.savedCardData.company);
      case 'saved_date':
        return new Date(b.savedAt).getTime() - new Date(a.savedAt).getTime();
      default:
        return 0;
    }
  });

  if (loading) {
    return (
      <div className="text-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">Loading saved contacts...</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Saved Contacts</h2>
        <p className="text-gray-600">Manage your collection of saved business contacts</p>
      </div>

      {/* Search and Filters */}
      <div className="bg-white rounded-xl shadow-lg p-4 sm:p-6">
        <div className="flex flex-col lg:flex-row gap-4">
          {/* Search */}
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="text"
              placeholder="Search by name, company, title, or email..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {/* Tag Filter */}
          <div className="relative">
            <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
            <select
              value={selectedTag}
              onChange={(e) => setSelectedTag(e.target.value)}
              className="pl-10 pr-8 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">All Tags</option>
              {tags.map(tag => (
                <option key={tag} value={tag}>{tag}</option>
              ))}
            </select>
          </div>

          {/* Sort */}
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as 'name' | 'company' | 'saved_date')}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="saved_date">Sort by Date Saved</option>
            <option value="name">Sort by Name</option>
            <option value="company">Sort by Company</option>
          </select>
        </div>
      </div>

      {/* Cards Grid */}
      {sortedCards.length > 0 ? (
        <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
          {sortedCards.map((savedCard) => (
            <div key={savedCard.id} className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-200">
              {/* Card Header */}
              <div className="p-4 sm:p-6 border-b border-gray-100">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center min-w-0 flex-1">
                    <img 
                      src={savedCard.savedCardData.profileImage} 
                      alt={savedCard.savedCardData.name}
                      className="w-12 h-12 rounded-full object-cover mr-3 flex-shrink-0"
                    />
                    <div className="min-w-0 flex-1">
                      <h3 className="font-semibold text-gray-900 truncate">{savedCard.savedCardData.name}</h3>
                      <p className="text-sm text-gray-600 truncate">{savedCard.savedCardData.title}</p>
                    </div>
                  </div>
                  <div className="flex space-x-1 ml-2">
                    <button
                      onClick={() => handleViewCard(savedCard)}
                      className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                      title="View Card"
                    >
                      <Eye className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => handleDownloadVCard(savedCard)}
                      className="p-2 text-gray-400 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors"
                      title="Download vCard"
                    >
                      <Download className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => handleDeleteCard(savedCard.id)}
                      className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                      title="Remove Card"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>

                {/* Company */}
                <div className="flex items-center text-sm text-gray-600 mb-2">
                  <Building className="w-4 h-4 mr-2 flex-shrink-0" />
                  <span className="truncate">{savedCard.savedCardData.company}</span>
                </div>

                {/* Contact Info */}
                <div className="space-y-1">
                  {savedCard.savedCardData.email && (
                    <div className="flex items-center text-sm text-gray-600">
                      <Mail className="w-4 h-4 mr-2 flex-shrink-0" />
                      <span className="truncate">{savedCard.savedCardData.email}</span>
                    </div>
                  )}
                  {savedCard.savedCardData.phone && (
                    <div className="flex items-center text-sm text-gray-600">
                      <Phone className="w-4 h-4 mr-2 flex-shrink-0" />
                      <span className="truncate">{savedCard.savedCardData.phone}</span>
                    </div>
                  )}
                  {savedCard.savedCardData.website && (
                    <div className="flex items-center text-sm text-gray-600">
                      <Globe className="w-4 h-4 mr-2 flex-shrink-0" />
                      <span className="truncate">{savedCard.savedCardData.website}</span>
                    </div>
                  )}
                </div>
              </div>

              {/* Card Footer */}
              <div className="p-4 bg-gray-50">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center text-xs text-gray-500">
                    <Calendar className="w-3 h-3 mr-1" />
                    Saved {new Date(savedCard.savedAt).toLocaleDateString()}
                  </div>
                  <button
                    onClick={() => {
                      setSelectedCard(savedCard);
                      setNoteText(savedCard.notes || '');
                      setShowAddNoteModal(true);
                    }}
                    className="text-xs text-blue-600 hover:text-blue-700 flex items-center"
                  >
                    <MessageSquare className="w-3 h-3 mr-1" />
                    {savedCard.notes ? 'Edit Note' : 'Add Note'}
                  </button>
                </div>

                {/* Notes */}
                {savedCard.notes && (
                  <p className="text-xs text-gray-600 bg-white p-2 rounded border italic mb-2">
                    "{savedCard.notes}"
                  </p>
                )}

                {/* Tags */}
                {savedCard.tags && savedCard.tags.length > 0 && (
                  <div className="flex flex-wrap gap-1">
                    {savedCard.tags.map(tag => (
                      <span key={tag} className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800">
                        <Tag className="w-3 h-3 mr-1" />
                        {tag}
                      </span>
                    ))}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12 bg-white rounded-xl shadow-lg">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <User className="w-8 h-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">No saved contacts yet</h3>
          <p className="text-gray-600 mb-4 px-4">
            {searchTerm || selectedTag !== 'all' 
              ? 'No cards match your current filters' 
              : 'Start building your network by saving business cards you encounter'
            }
          </p>
          {searchTerm || selectedTag !== 'all' ? (
            <button
              onClick={() => {
                setSearchTerm('');
                setSelectedTag('all');
              }}
              className="text-blue-600 hover:text-blue-700 font-medium"
            >
              Clear filters
            </button>
          ) : null}
        </div>
      )}

      {/* Add Note Modal */}
      {showAddNoteModal && selectedCard && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl shadow-2xl w-full max-w-md">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">
                {selectedCard.notes ? 'Edit Note' : 'Add Note'}
              </h3>
              <p className="text-sm text-gray-600 mt-1">
                For {selectedCard.savedCardData.name}
              </p>
            </div>
            
            <div className="p-6">
              <textarea
                value={noteText}
                onChange={(e) => setNoteText(e.target.value)}
                placeholder="Add a personal note about this contact..."
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
              />
            </div>

            <div className="p-6 border-t border-gray-200 flex flex-col sm:flex-row justify-end space-y-3 sm:space-y-0 sm:space-x-3">
              <button
                onClick={() => {
                  setShowAddNoteModal(false);
                  setNoteText('');
                  setSelectedCard(null);
                }}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors order-2 sm:order-1"
              >
                Cancel
              </button>
              <button
                onClick={handleAddNote}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors order-1 sm:order-2"
              >
                Save Note
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}