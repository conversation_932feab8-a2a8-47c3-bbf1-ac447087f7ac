import React, { useState } from 'react';
import { Image, Layers, Palette, Crown, Check, Upload, X } from 'lucide-react';
import { PageBackground } from '../types';

interface PageBackgroundSelectorProps {
  currentBackground?: PageBackground;
  onBackgroundChange: (background: PageBackground | null) => void;
  canChangeBackground: boolean;
  onUpgradeClick?: () => void;
}

const gradientBackgrounds = [
  {
    id: 'none',
    name: 'Default',
    value: 'bg-gradient-to-br from-gray-50 to-blue-50',
    preview: 'linear-gradient(135deg, #f9fafb 0%, #eff6ff 100%)',
    isPremium: false
  },
  {
    id: 'ocean',
    name: 'Ocean Breeze',
    value: 'bg-gradient-to-br from-blue-400 via-blue-500 to-cyan-400',
    preview: 'linear-gradient(135deg, #60a5fa 0%, #3b82f6 50%, #22d3ee 100%)',
    isPremium: false
  },
  {
    id: 'sunset',
    name: 'Sunset Glow',
    value: 'bg-gradient-to-br from-orange-400 via-pink-500 to-purple-600',
    preview: 'linear-gradient(135deg, #fb923c 0%, #ec4899 50%, #9333ea 100%)',
    isPremium: false
  },
  {
    id: 'forest',
    name: 'Forest Haze',
    value: 'bg-gradient-to-br from-green-400 via-green-600 to-lime-400',
    preview: 'linear-gradient(135deg, #4ade80 0%, #16a34a 50%, #a3e635 100%)',
    isPremium: false
  },
  {
    id: 'royal',
    name: 'Royal Purple',
    value: 'bg-gradient-to-br from-purple-500 via-indigo-500 to-blue-500',
    preview: 'linear-gradient(135deg, #a78bfa 0%, #6366f1 50%, #3b82f6 100%)',
    isPremium: false
  },
  {
    id: 'midnight',
    name: 'Midnight Blue',
    value: 'bg-gradient-to-br from-gray-800 via-blue-900 to-black',
    preview: 'linear-gradient(135deg, #1e293b 0%, #1e40af 50%, #000000 100%)',
    isPremium: false
  },
  {
    id: 'rose',
    name: 'Rose Gold',
    value: 'bg-gradient-to-br from-pink-400 via-rose-500 to-red-400',
    preview: 'linear-gradient(135deg, #f472b6 0%, #f43f5e 50%, #ef4444 100%)',
    isPremium: false
  }
];

const patternBackgrounds = [
  {
    id: 'dots',
    name: 'Subtle Dots',
    value: 'bg-gray-50',
    pattern: 'radial-gradient(circle at 1px 1px, rgba(0,0,0,0.05) 1px, transparent 0)',
    patternSize: '20px 20px',
    isPremium: true
  },
  {
    id: 'grid',
    name: 'Grid Lines',
    value: 'bg-gray-50',
    pattern: 'linear-gradient(rgba(0,0,0,0.03) 1px, transparent 1px), linear-gradient(90deg, rgba(0,0,0,0.03) 1px, transparent 1px)',
    patternSize: '20px 20px',
    isPremium: true
  },
  {
    id: 'diagonal',
    name: 'Diagonal Lines',
    value: 'bg-gray-50',
    pattern: 'repeating-linear-gradient(45deg, transparent, transparent 10px, rgba(0,0,0,0.03) 10px, rgba(0,0,0,0.03) 20px)',
    patternSize: 'auto',
    isPremium: true
  }
];

const imageBackgrounds = [
  {
    id: 'abstract-1',
    name: 'Abstract Waves',
    url: 'https://images.pexels.com/photos/1103970/pexels-photo-1103970.jpeg?auto=compress&cs=tinysrgb&w=1920',
    isPremium: true
  },
  {
    id: 'geometric-1',
    name: 'Geometric Shapes',
    url: 'https://images.pexels.com/photos/1323712/pexels-photo-1323712.jpeg?auto=compress&cs=tinysrgb&w=1920',
    isPremium: true
  },
  {
    id: 'minimal-1',
    name: 'Minimal Texture',
    url: 'https://images.pexels.com/photos/1629236/pexels-photo-1629236.jpeg?auto=compress&cs=tinysrgb&w=1920',
    isPremium: true
  }
];

export default function PageBackgroundSelector({ 
  currentBackground, 
  onBackgroundChange, 
  canChangeBackground, 
  onUpgradeClick 
}: PageBackgroundSelectorProps) {
  const [activeTab, setActiveTab] = useState<'gradient' | 'pattern' | 'image'>('gradient');
  const [uploadingImage, setUploadingImage] = useState(false);

  const handleBackgroundSelect = (type: 'gradient' | 'image' | 'pattern', value: string, isPremium: boolean, pattern?: string) => {
    if (isPremium && !canChangeBackground) {
      onUpgradeClick?.();
      return;
    }

    const background: PageBackground = {
      type,
      value: type === 'pattern' && pattern ? pattern : value,
      overlay: {
        enabled: type === 'image',
        color: '#000000',
        opacity: 0.3
      }
    };

    onBackgroundChange(background);
  };

  const handleImageUpload = async (file: File) => {
    if (!canChangeBackground) {
      onUpgradeClick?.();
      return;
    }

    if (!file.type.startsWith('image/')) {
      alert('Please select an image file');
      return;
    }

    if (file.size > 10 * 1024 * 1024) { // 10MB limit
      alert('Image size must be less than 10MB');
      return;
    }

    setUploadingImage(true);

    try {
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        const background: PageBackground = {
          type: 'image',
          value: result,
          overlay: {
            enabled: true,
            color: '#000000',
            opacity: 0.3
          }
        };
        onBackgroundChange(background);
        setUploadingImage(false);
      };
      reader.readAsDataURL(file);
    } catch (error) {
      console.error('Error uploading image:', error);
      alert('Failed to upload image. Please try again.');
      setUploadingImage(false);
    }
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (files.length > 0) {
      handleImageUpload(files[0]);
    }
  };

  const isCurrentBackground = (type: string, value: string) => {
    if (!currentBackground) return type === 'none';
    return currentBackground.type === 'gradient' && currentBackground.value.includes(value.split(' ').pop() || '');
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900 flex items-center">
          <Layers className="w-5 h-5 mr-2" />
          Page Background
        </h3>
        {!canChangeBackground && (
          <button
            onClick={onUpgradeClick}
            className="text-sm text-blue-600 hover:text-blue-700 font-medium flex items-center"
          >
            <Crown className="w-4 h-4 mr-1" />
            Upgrade for Premium Backgrounds
          </button>
        )}
      </div>

      {/* Tab Navigation */}
      <div className="flex space-x-1 bg-gray-100 rounded-lg p-1">
        <button
          onClick={() => setActiveTab('gradient')}
          className={`flex-1 flex items-center justify-center px-3 py-2 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'gradient'
              ? 'bg-white text-gray-900 shadow-sm'
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          <Palette className="w-4 h-4 mr-2" />
          Gradients
        </button>
        <button
          onClick={() => setActiveTab('pattern')}
          className={`flex-1 flex items-center justify-center px-3 py-2 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'pattern'
              ? 'bg-white text-gray-900 shadow-sm'
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          <Layers className="w-4 h-4 mr-2" />
          Patterns
        </button>
        <button
          onClick={() => setActiveTab('image')}
          className={`flex-1 flex items-center justify-center px-3 py-2 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'image'
              ? 'bg-white text-gray-900 shadow-sm'
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          <Image className="w-4 h-4 mr-2" />
          Images
        </button>
      </div>

      {/* Current Background Display */}
      {currentBackground && (
        <div className="bg-gray-50 rounded-lg p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700">Current Background</span>
            <button
              onClick={() => onBackgroundChange(null)}
              className="text-red-600 hover:text-red-700 text-sm flex items-center"
            >
              <X className="w-4 h-4 mr-1" />
              Remove
            </button>
          </div>
          <div 
            className="w-full h-16 rounded-lg border-2 border-blue-500"
            style={{
              background: currentBackground.type === 'gradient' 
                ? currentBackground.value.replace('bg-gradient-to-br', 'linear-gradient(135deg,').replace(/from-(\w+)-(\d+)/g, 'var(--tw-gradient-from)').replace(/via-(\w+)-(\d+)/g, 'var(--tw-gradient-via)').replace(/to-(\w+)-(\d+)/g, 'var(--tw-gradient-to)')
                : currentBackground.type === 'image'
                ? `url(${currentBackground.value}) center/cover`
                : currentBackground.value
            }}
          ></div>
        </div>
      )}

      {/* Tab Content */}
      {activeTab === 'gradient' && (
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
          {gradientBackgrounds.map((bg) => {
            const isLocked = bg.isPremium && !canChangeBackground;
            const isSelected = isCurrentBackground('gradient', bg.value);

            return (
              <div
                key={bg.id}
                className={`relative cursor-pointer rounded-xl overflow-hidden border-2 transition-all duration-200 ${
                  isSelected 
                    ? 'border-blue-500 ring-2 ring-blue-200' 
                    : 'border-gray-200 hover:border-gray-300'
                } ${isLocked ? 'opacity-60' : ''}`}
                onClick={() => handleBackgroundSelect('gradient', bg.value, bg.isPremium)}
              >
                <div 
                  className="h-20 relative"
                  style={{ background: bg.preview }}
                >
                  {/* Selected Indicator */}
                  {isSelected && (
                    <div className="absolute top-2 right-2 w-5 h-5 bg-white rounded-full flex items-center justify-center">
                      <Check className="w-3 h-3 text-blue-600" />
                    </div>
                  )}

                  {/* Premium Badge */}
                  {bg.isPremium && (
                    <div className="absolute top-2 left-2">
                      <Crown className="w-4 h-4 text-yellow-400" />
                    </div>
                  )}

                  {/* Lock Indicator */}
                  {isLocked && (
                    <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center">
                      <div className="bg-white rounded-full p-1">
                        <svg className="w-3 h-3 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                        </svg>
                      </div>
                    </div>
                  )}
                </div>

                <div className="p-3 bg-white">
                  <h4 className="font-medium text-gray-900 text-sm">{bg.name}</h4>
                </div>
              </div>
            );
          })}
        </div>
      )}

      {activeTab === 'pattern' && (
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
          {patternBackgrounds.map((bg) => {
            const isLocked = bg.isPremium && !canChangeBackground;
            const isSelected = currentBackground?.type === 'pattern' && currentBackground.value === bg.pattern;

            return (
              <div
                key={bg.id}
                className={`relative cursor-pointer rounded-xl overflow-hidden border-2 transition-all duration-200 ${
                  isSelected 
                    ? 'border-blue-500 ring-2 ring-blue-200' 
                    : 'border-gray-200 hover:border-gray-300'
                } ${isLocked ? 'opacity-60' : ''}`}
                onClick={() => handleBackgroundSelect('pattern', bg.pattern, bg.isPremium, bg.pattern)}
              >
                <div 
                  className="h-20 relative"
                  style={{ 
                    backgroundColor: '#f9fafb',
                    backgroundImage: bg.pattern,
                    backgroundSize: bg.patternSize
                  }}
                >
                  {/* Selected Indicator */}
                  {isSelected && (
                    <div className="absolute top-2 right-2 w-5 h-5 bg-white rounded-full flex items-center justify-center">
                      <Check className="w-3 h-3 text-blue-600" />
                    </div>
                  )}

                  {/* Premium Badge */}
                  {bg.isPremium && (
                    <div className="absolute top-2 left-2">
                      <Crown className="w-4 h-4 text-yellow-400" />
                    </div>
                  )}

                  {/* Lock Indicator */}
                  {isLocked && (
                    <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center">
                      <div className="bg-white rounded-full p-1">
                        <svg className="w-3 h-3 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                        </svg>
                      </div>
                    </div>
                  )}
                </div>

                <div className="p-3 bg-white">
                  <h4 className="font-medium text-gray-900 text-sm">{bg.name}</h4>
                </div>
              </div>
            );
          })}
        </div>
      )}

      {activeTab === 'image' && (
        <div className="space-y-4">
          {/* Upload Custom Image */}
          <div>
            <div className="text-sm font-medium text-gray-700 mb-2">Upload Custom Background</div>
            <label className={`flex items-center justify-center w-full h-32 border-2 border-dashed border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors ${
              !canChangeBackground ? 'opacity-50 cursor-not-allowed' : ''
            }`}>
              <div className="text-center">
                {uploadingImage ? (
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
                ) : (
                  <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                )}
                <p className="text-sm text-gray-600">
                  {uploadingImage ? 'Uploading...' : 'Click to upload or drag and drop'}
                </p>
                <p className="text-xs text-gray-500">PNG, JPG up to 10MB</p>
              </div>
              <input
                type="file"
                accept="image/*"
                onChange={handleFileInputChange}
                className="hidden"
                disabled={!canChangeBackground || uploadingImage}
              />
            </label>
            {!canChangeBackground && (
              <p className="text-xs text-gray-500 mt-1">Premium feature</p>
            )}
          </div>

          {/* Preset Images */}
          <div>
            <div className="text-sm font-medium text-gray-700 mb-2">Preset Backgrounds</div>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              {imageBackgrounds.map((bg) => {
                const isLocked = bg.isPremium && !canChangeBackground;
                const isSelected = currentBackground?.type === 'image' && currentBackground.value === bg.url;

                return (
                  <div
                    key={bg.id}
                    className={`relative cursor-pointer rounded-xl overflow-hidden border-2 transition-all duration-200 ${
                      isSelected 
                        ? 'border-blue-500 ring-2 ring-blue-200' 
                        : 'border-gray-200 hover:border-gray-300'
                    } ${isLocked ? 'opacity-60' : ''}`}
                    onClick={() => handleBackgroundSelect('image', bg.url, bg.isPremium)}
                  >
                    <img 
                      src={bg.url} 
                      alt={bg.name}
                      className="w-full h-20 object-cover"
                    />
                    
                    {/* Selected Indicator */}
                    {isSelected && (
                      <div className="absolute top-2 right-2 w-5 h-5 bg-white rounded-full flex items-center justify-center">
                        <Check className="w-3 h-3 text-blue-600" />
                      </div>
                    )}

                    {/* Premium Badge */}
                    {bg.isPremium && (
                      <div className="absolute top-2 left-2">
                        <Crown className="w-4 h-4 text-yellow-400" />
                      </div>
                    )}

                    {/* Lock Indicator */}
                    {isLocked && (
                      <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center">
                        <div className="bg-white rounded-full p-1">
                          <svg className="w-3 h-3 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                          </svg>
                        </div>
                      </div>
                    )}

                    <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white text-xs p-2 text-center">
                      {bg.name}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      )}

      {!canChangeBackground && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <p className="text-sm text-blue-800">
            <strong>Unlock Premium Page Backgrounds:</strong> Upgrade to Unlimited or higher to access premium gradients, patterns, and custom background images for your entire page.
          </p>
        </div>
      )}
    </div>
  );
}