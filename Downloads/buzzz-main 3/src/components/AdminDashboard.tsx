import React, { useState, useEffect } from 'react';
import { Users, CreditCard, DollarSign, BarChart3, Search, Filter, Trash2, Mail, FileText } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { supabase } from '../lib/supabase';
import UserTypeIndicator from './UserTypeIndicator';
import { UserProfile, UserType } from '../types';
import AdminEmailDashboard from './AdminEmailDashboard';
import EmailTemplateManager from './EmailTemplateManager';
import EmailTemplates from './EmailTemplates';
import SubscriptionManager from './SubscriptionManager';

interface AdminStats {
  totalUsers: number;
  activeSubscriptions: number;
  totalRevenue: number;
  totalCards: number;
  totalSubscriptions?: number;
  subscriptionStatuses?: Record<string, number>;
}

interface UserWithProfile {
  id: string;
  email?: string;
  created_at?: string;
  profile: UserProfile | null;
}

export default function AdminDashboard() {
  const { hasPermission, session, userProfile } = useAuth();
  const [activeTab, setActiveTab] = useState<'overview' | 'users' | 'emails' | 'templates' | 'examples' | 'subscriptions'>('overview');
  const [stats, setStats] = useState<AdminStats>({
    totalUsers: 0,
    activeSubscriptions: 0,
    totalRevenue: 0,
    totalCards: 0
  });
  const [users, setUsers] = useState<UserWithProfile[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<UserType | 'all'>('all');
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const [deletingUsers, setDeletingUsers] = useState<Set<string>>(new Set());
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [showRenewalModal, setShowRenewalModal] = useState(false);
  const [renewalUser, setRenewalUser] = useState<{ id: string; userType: string } | null>(null);
  const [renewalDuration, setRenewalDuration] = useState(30);
  const [renewalLoading, setRenewalLoading] = useState(false);

  // Debug logging
  console.log('AdminDashboard render:', {
    hasPermission: hasPermission('admin'),
    session: !!session,
    userProfile: userProfile ? {
      userType: userProfile.userType,
      hasAdmin: userProfile.userType === 'super_admin'
    } : null
  });

  // Function to analyze subscription data in detail
  const analyzeSubscriptions = async () => {
    try {
      console.log('=== ANALYZING SUBSCRIPTIONS ===');
      
      // Check all subscriptions regardless of status
      const { data: allSubscriptions, error: allError } = await supabase
        .from('stripe_subscriptions')
        .select('*');
      
      if (allError) {
        console.error('Error loading all subscriptions:', allError);
        return;
      }
      
      console.log('All stripe_subscriptions:', allSubscriptions);
      
      if (allSubscriptions && allSubscriptions.length > 0) {
        // Group by status
        const statusGroups = allSubscriptions.reduce((groups, sub) => {
          const status = sub.status || 'unknown';
          if (!groups[status]) groups[status] = [];
          groups[status].push(sub);
          return groups;
        }, {});
        
        console.log('Subscriptions grouped by status:', statusGroups);
        
        // Check for different status values
        const uniqueStatuses = [...new Set(allSubscriptions.map(s => s.status))];
        console.log('Unique subscription statuses found:', uniqueStatuses);
        
        // Check subscription details
        const subscriptionSamples = allSubscriptions.slice(0, 5).map(s => ({
          id: s.id,
          customer_id: s.customer_id,
          subscription_id: s.subscription_id,
          price_id: s.price_id,
          status: s.status,
          current_period_start: s.current_period_start,
          current_period_end: s.current_period_end
        }));
        console.log('Subscription detail samples:', subscriptionSamples);
        
        // Also check stripe_orders for payment data
        const { data: ordersData } = await supabase
          .from('stripe_orders')
          .select('*');
        
        console.log('Stripe orders data:', ordersData);
        
        // Check for any pending checkout sessions or incomplete payments
        console.log('=== CHECKING FOR INCOMPLETE PAYMENTS ===');
        
        // Look for subscriptions that might need manual activation
        const incompleteSubs = allSubscriptions.filter(s => 
          s.status === 'not_started' && 
          (!s.subscription_id || !s.price_id)
        );
        
        if (incompleteSubs.length > 0) {
          console.log('Found incomplete subscriptions that need attention:', incompleteSubs);
          console.log('These subscriptions are stuck in "not_started" state');
          console.log('Possible causes:');
          console.log('1. User abandoned checkout process');
          console.log('2. Webhook failed to fire after payment');
          console.log('3. Payment was made but subscription not activated');
        }
        
        // Check if we can find the customer in stripe_customers
        for (const sub of allSubscriptions) {
          if (sub.customer_id) {
            const { data: customerData } = await supabase
              .from('stripe_customers')
              .select('user_id, customer_id')
              .eq('customer_id', sub.customer_id);
            
            console.log(`Customer ${sub.customer_id} maps to user:`, customerData);
          }
        }
      } else {
        console.log('No stripe_subscriptions found in database');
      }
      
      console.log('=== END SUBSCRIPTION ANALYSIS ===');
    } catch (error) {
      console.error('Error analyzing subscriptions:', error);
    }
  };

  useEffect(() => {
    if (hasPermission('admin')) {
      loadAdminData();
    }
  }, [hasPermission]);

  // Cleanup timeouts on unmount
  useEffect(() => {
    return () => {
      // Clear any pending timeouts
      setError(null);
      setSuccessMessage(null);
    };
  }, []);

  const setSuccessMessageWithTimeout = (message: string, timeout: number = 5000) => {
    setSuccessMessage(message);
    setTimeout(() => {
      setSuccessMessage(null);
    }, timeout);
  };

  const setErrorWithTimeout = (message: string, timeout: number = 8000) => {
    setError(message);
    setTimeout(() => {
      setError(null);
    }, timeout);
  };

  const loadAdminData = async () => {
    try {
      setError(null);
      setSuccessMessage(null);
      
      console.log('Loading admin analytics data...');
      
      // Load stats more consistently and accurately with better error handling
      const [usersResult, subscriptionsResult, allSubscriptionsResult, cardsResult] = await Promise.all([
        supabase.from('user_profiles').select('*'),
        supabase.from('stripe_subscriptions').select('*').eq('status', 'active'),
        supabase.from('stripe_subscriptions').select('*'), // Get all subscriptions for analysis
        supabase.from('business_cards').select('*')
      ]);
      
      // Try to get auth users count separately with error handling
      let authUsersCount = 0;
      try {
        // Note: supabase.auth.admin.listUsers() requires service role key and can't be called from frontend
        // Instead, we'll get the count from user_profiles table which is accessible
        const { count } = await supabase
          .from('user_profiles')
          .select('*', { count: 'exact', head: true });
        authUsersCount = count || 0;
        console.log('Got user count from user_profiles:', authUsersCount);
      } catch (error) {
        console.error('Error getting user count:', error);
        authUsersCount = 0;
      }
      
      // If still no count, try a different approach - check if we can query the users table
      if (authUsersCount === 0) {
        console.log('No users found in user_profiles, this might indicate a data sync issue');
        authUsersCount = 0;
      }
      
      // Note: We can't access auth.users directly from frontend due to RLS policies
      // The user count will come from user_profiles table which is accessible
      
      // Let's also check what's actually in user_profiles to debug the missing users
      console.log('=== USER_PROFILES DEBUG ===');
      console.log('user_profiles table data:', usersResult.data);
      console.log('user_profiles count:', usersResult.data?.length || 0);
      
      // Check for errors in each query
      if (usersResult.error) {
        console.error('Error loading user profiles:', usersResult.error);
      }
      if (subscriptionsResult.error) {
        console.error('Error loading subscriptions:', subscriptionsResult.error);
      }
      if (cardsResult.error) {
        console.error('Error loading business cards:', cardsResult.error);
      }

      // Calculate total users from user_profiles
      const totalUsers = usersResult.data?.length || 0;
      
      // Debug user count discrepancy
      console.log('=== USER COUNT DEBUG ===');
      console.log('user_profiles count:', usersResult.data?.length || 0);
      console.log('auth.users count:', authUsersCount);
      console.log('user_profiles data:', usersResult.data?.map(p => ({ user_id: p.user_id, user_type: p.user_type })));
      
      // Use the higher count to avoid missing users
      let actualUserCount = Math.max(totalUsers, authUsersCount);
      console.log('Using user count:', actualUserCount);
      
      // IMPORTANT: The edge function shows 7 users, but user_profiles only has 1
      // This means Gmail signups aren't creating user_profiles entries
      // We'll use the edge function count when it's available
      console.log('Note: Edge function will be called later and should show 7 users');
      
      // Auto-create profile for current user if they don't have one
      if (session?.user?.id) {
        try {
          const { data: currentProfile, error: profileCheckError } = await supabase
            .from('user_profiles')
            .select('id')
            .eq('user_id', session.user.id)
            .single();
          
          if (profileCheckError && profileCheckError.code === 'PGRST116') {
            // Profile doesn't exist, create one
            console.log('Current user has no profile, creating one...');
            const { error: createError } = await supabase
              .from('user_profiles')
              .insert({
                user_id: session.user.id,
                user_type: 'user',
                subscription_status: 'free',
                has_analytics: false,
                has_custom_domains: false,
                has_ecommerce: false,
                can_change_background: true,
                max_offers: 3
              });
            
            if (createError) {
              console.error('Error creating profile for current user:', createError);
            } else {
              console.log('✅ Created profile for current user');
            }
          }
        } catch (error) {
          console.error('Error checking/creating current user profile:', error);
        }
      }
      
      // Calculate active subscriptions and revenue more accurately
      const activeSubscriptions = subscriptionsResult.data?.length || 0;
      
      // For stripe_subscriptions, we need to calculate revenue differently since it doesn't store amount directly
      // We'll use a default value or fetch from stripe_orders if available
      let totalRevenue = 0;
      
      // Try to get revenue from stripe_orders table if available
      try {
        const { data: ordersData } = await supabase
          .from('stripe_orders')
          .select('amount_total, currency')
          .eq('status', 'completed');
        
        console.log('Stripe orders for revenue:', ordersData);
        
        if (ordersData && ordersData.length > 0) {
          totalRevenue = ordersData.reduce((sum, order) => {
            // Convert from cents to dollars (Stripe stores amounts in cents)
            const amount = (order.amount_total || 0) / 100;
            return sum + amount;
          }, 0);
        } else {
          console.log('No completed stripe_orders found for revenue calculation');
        }
      } catch (error) {
        console.log('Could not fetch stripe_orders for revenue calculation:', error);
      }
      
      // If no revenue from orders, try to estimate from active subscriptions
      if (totalRevenue === 0 && activeSubscriptions > 0) {
        console.log('Estimating revenue from active subscriptions...');
        // Check if subscriptions have price_id to determine amount
        const { data: priceData } = await supabase
          .from('stripe_subscriptions')
          .select('price_id, status')
          .eq('status', 'active');
        
        console.log('Active subscription price data:', priceData);
        
        // Estimate based on price_id (you can customize these amounts)
        const priceMap: Record<string, number> = {
          'price_1RcMHkBZkbtRKmT0yUuQbQYd': 9.99, // unlimited_monthly
          'price_1RcMFyBZkbtRKmT0HUexJJVs': 99.99, // unlimited_yearly
          'price_1RcMF1BZkbtRKmT0B42SAYsQ': 19.99, // super_unlimited
        };
        
        totalRevenue = priceData?.reduce((sum, sub) => {
          const amount = priceMap[sub.price_id || ''] || 9.99; // Default to $9.99
          return sum + amount;
        }, 0) || 0;
        
        console.log('Estimated revenue from subscriptions:', totalRevenue);
      }

      // Analyze all subscriptions for better insights
      const allSubscriptions = allSubscriptionsResult.data || [];
      const subscriptionStatuses = allSubscriptions.reduce((acc, sub) => {
        const status = sub.status || 'unknown';
        acc[status] = (acc[status] || 0) + 1;
        return acc;
      }, {});
      
      const totalSubscriptions = allSubscriptions.length;
      
      // Calculate total business cards
      const totalCards = cardsResult.data?.length || 0;

      // Log detailed analytics data for debugging
      console.log('=== ANALYTICS DEBUG DATA ===');
      console.log('User Profiles:', {
        count: usersResult.data?.length || 0,
        data: usersResult.data?.slice(0, 3) || [], // Show first 3 for debugging
        error: usersResult.error
      });
      console.log('Subscriptions:', {
        count: subscriptionsResult.data?.length || 0,
        data: subscriptionsResult.data?.slice(0, 3) || [], // Show first 3 for debugging
        error: subscriptionsResult.error,
        sampleAmounts: subscriptionsResult.data?.map(s => s.amount).slice(0, 5) || []
      });
      console.log('Business Cards:', {
        count: cardsResult.data?.length || 0,
        data: cardsResult.data?.slice(0, 3) || [], // Show first 3 for debugging
        error: cardsResult.error
      });
      console.log('Calculated Stats:', {
        totalUsers,
        activeSubscriptions,
        totalRevenue,
        totalCards
      });
      console.log('===========================');

      setStats({
        totalUsers: actualUserCount,
        activeSubscriptions,
        totalRevenue,
        totalCards,
        totalSubscriptions,
        subscriptionStatuses
      });
      setLastUpdated(new Date());

      // Load users via edge function
      await loadUsersFromEdgeFunction();
    } catch (error: any) {
      console.error('Error loading admin data:', error);
      setErrorWithTimeout('Failed to load admin data');
    } finally {
      setLoading(false);
    }
  };

  const loadUsersFromEdgeFunction = async () => {
    try {
      setError(null);
      setSuccessMessage(null);
      
      if (!session?.access_token) {
        console.error('No access token available');
        throw new Error('No access token available');
      }

      const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
      if (!supabaseUrl) {
        throw new Error('VITE_SUPABASE_URL environment variable is not set');
      }

      const functionUrl = `${supabaseUrl}/functions/v1/admin-users`;
      console.log('Calling edge function at:', functionUrl);

      console.log('Calling edge function with token:', session.access_token ? 'Token present' : 'No token');
      
      const response = await fetch(functionUrl, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json',
        },
      });

      console.log('Edge function response status:', response.status);
      console.log('Edge function response headers:', Object.fromEntries(response.headers.entries()));

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Edge function error response:', errorText);
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const data = await response.json();
      console.log('Edge function success response:', data);
      console.log('Edge function users count:', data.users?.length || 0);
      console.log('Edge function users data:', data.users?.slice(0, 3) || []);
      setUsers(data.users || []);
      
      // IMPORTANT: Update the stats with the real user count from edge function
      if (data.users && data.users.length > 0) {
        const realUserCount = data.users.length;
        console.log('🔄 Updating stats with real user count from edge function:', realUserCount);
        
        // Update the stats state with the correct user count
        setStats(prevStats => ({
          ...prevStats,
          totalUsers: realUserCount
        }));
      }
    } catch (error: any) {
      console.error('Error loading users from edge function:', error);
      setErrorWithTimeout(`Failed to load users: ${error.message}`);
      
      // Fallback to client-side data without emails
      try {
        const { data: profiles } = await supabase.from('user_profiles').select('*');
        const usersWithProfiles: UserWithProfile[] = profiles?.map(profile => ({
          id: profile.user_id,
          email: undefined,
          created_at: profile.created_at,
          profile: profile
        })) || [];
        setUsers(usersWithProfiles);
        setErrorWithTimeout('Loaded users without email data (edge function unavailable)');
      } catch (fallbackError) {
        console.error('Fallback also failed:', fallbackError);
        setErrorWithTimeout('Failed to load users even with fallback method');
      }
    }
  };

  const updateUserType = async (userId: string, newType: UserType, subscriptionEndDate?: string) => {
    try {
      setError(null);
      setSuccessMessage(null);
      
      if (!session?.access_token) {
        throw new Error('No access token available');
      }

      const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
      if (!supabaseUrl) {
        throw new Error('VITE_SUPABASE_URL environment variable is not set');
      }

      // Calculate subscription end date if not provided
      let endDate = subscriptionEndDate;
      if (!endDate && newType !== 'free') {
        // Default to 30 days from now for premium plans
        const defaultEndDate = new Date();
        defaultEndDate.setDate(defaultEndDate.getDate() + 30);
        endDate = defaultEndDate.toISOString();
      }

      const response = await fetch(`${supabaseUrl}/functions/v1/admin-users`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          userType: newType,
          subscriptionEndDate: endDate,
          subscriptionStatus: newType === 'free' ? 'expired' : 'active'
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update user');
      }

      // Reload users after successful update
      await loadUsersFromEdgeFunction();
      setSuccessMessageWithTimeout(`User type updated successfully`);
    } catch (error: any) {
      console.error('Error updating user type:', error);
      setErrorWithTimeout(`Failed to update user: ${error.message}`);
      
      // Fallback to client-side update
      try {
        const { error } = await supabase
          .from('user_profiles')
          .update({
            user_type: newType,
            subscription_status: newType === 'free' ? 'expired' : 'active',
            subscription_end_date: subscriptionEndDate || (newType === 'free' ? null : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()),
            max_offers: newType === 'free' ? 3 : -1,
            has_analytics: newType !== 'free',
            has_ecommerce: newType === 'super_unlimited',
            can_change_background: newType !== 'free',
            updated_at: new Date().toISOString()
          })
          .eq('user_id', userId);

        if (!error) {
          await loadAdminData();
          setSuccessMessageWithTimeout('User updated using fallback method');
        }
              } catch (fallbackError) {
          console.error('Fallback update also failed:', fallbackError);
          setErrorWithTimeout('Failed to update user even with fallback method');
        }
    }
  };

  const deleteUser = async (userId: string) => {
    const userToDelete = users.find(u => u.id === userId);
    const userEmail = userToDelete?.email || userId;
    
    if (!confirm(`Are you sure you want to delete user "${userEmail}"?\n\nThis action will:\n• Permanently delete the user account\n• Remove all associated data\n• Cannot be undone\n\nType "DELETE" to confirm:`)) {
      return;
    }
    
    const confirmation = prompt('Please type "DELETE" to confirm user deletion:');
    if (confirmation !== 'DELETE') {
      return;
    }

    try {
      setError(null);
      setSuccessMessage(null);
      setDeletingUsers(prev => new Set(prev).add(userId));
      
      if (!session?.access_token) {
        throw new Error('No access token available');
      }

      const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
      if (!supabaseUrl) {
        throw new Error('VITE_SUPABASE_URL environment variable is not set');
      }

      console.log('Attempting to delete user:', userId);
      console.log('Using edge function URL:', `${supabaseUrl}/functions/v1/admin-users`);
      console.log('Access token present:', !!session.access_token);

      const response = await fetch(`${supabaseUrl}/functions/v1/admin-users`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId }),
      });

      console.log('Delete response status:', response.status);
      console.log('Delete response headers:', Object.fromEntries(response.headers.entries()));

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Delete error response:', errorText);
        let errorData;
        try {
          errorData = JSON.parse(errorText);
        } catch (e) {
          errorData = { error: errorText };
        }
        throw new Error(errorData.error || `HTTP ${response.status}: ${errorText}`);
      }

      const responseData = await response.json();
      console.log('Delete success response:', responseData);

      // Reload data after successful deletion
      await loadAdminData();
      setSuccessMessageWithTimeout(`User "${userEmail}" deleted successfully`);
    } catch (error: any) {
      console.error('Error deleting user:', error);
      
      // Provide more specific error messages
      let errorMessage = 'Failed to delete user';
      if (error.name === 'TypeError' && error.message.includes('Failed to fetch')) {
        errorMessage = 'Network error: Unable to connect to the server. Please check your internet connection and try again.';
      } else if (error.message) {
        errorMessage = error.message;
      }
      
      setErrorWithTimeout(errorMessage);
    } finally {
      setDeletingUsers(prev => {
        const newSet = new Set(prev);
        newSet.delete(userId);
        return newSet;
      });
    }
  };

  const testEdgeFunctionConnectivity = async () => {
    // This function is no longer needed - removed for cleaner code
  };

  const syncMissingUserProfiles = async () => {
    try {
      setError(null);
      setSuccessMessage(null);
      
      if (!session?.access_token) {
        throw new Error('No access token available');
      }

      const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
      if (!supabaseUrl) {
        throw new Error('VITE_SUPABASE_URL environment variable is not set');
      }

      console.log('Syncing missing user profiles...');
      
      const response = await fetch(`${supabaseUrl}/functions/v1/sync-user-profiles`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to sync user profiles');
      }

      const responseData = await response.json();
      console.log('Sync response:', responseData);

      if (responseData.success) {
        setSuccessMessageWithTimeout(`Successfully synced ${responseData.synced_count} user profiles!`);
        // Reload data to show updated counts
        await loadAdminData();
      } else {
        throw new Error(responseData.error || 'Sync failed');
      }
    } catch (error: any) {
      console.error('Error syncing user profiles:', error);
      setErrorWithTimeout(`Failed to sync user profiles: ${error.message}`);
    }
  };

  const createUserProfile = async (userId: string) => {
    try {
      setError(null);
      setSuccessMessage(null);
      
      const { error } = await supabase
        .from('user_profiles')
        .insert({
          user_id: userId,
          user_type: 'free',
          subscription_status: 'expired',
          max_offers: 3,
          has_analytics: false,
          has_ecommerce: false,
          can_change_background: false
        });

      if (error) throw error;

      await loadAdminData();
      setSuccessMessageWithTimeout('User profile created successfully');
    } catch (error: any) {
      console.error('Error creating user profile:', error);
      setErrorWithTimeout(`Failed to create user profile: ${error.message}`);
    }
  };

  const openRenewalModal = (userId: string, userType: string) => {
    setRenewalUser({ id: userId, userType });
    setShowRenewalModal(true);
  };

  const handleRenewal = async () => {
    if (!renewalUser) return;
    
    setRenewalLoading(true);
    try {
      const { error } = await supabase
        .from('user_profiles')
        .update({
          subscription_status: 'active',
          subscription_start_date: new Date().toISOString(),
          subscription_end_date: new Date(Date.now() + renewalDuration * 24 * 60 * 60 * 1000).toISOString()
        })
        .eq('user_id', renewalUser.id);
      
      if (error) throw error;
      
      setSuccessMessageWithTimeout('Subscription renewed successfully!');
      setShowRenewalModal(false);
      setRenewalUser(null);
      loadAdminData(); // Reload data
    } catch (error: any) {
      console.error('Error renewing subscription:', error);
      setErrorWithTimeout(`Failed to renew subscription: ${error.message}`);
    } finally {
      setRenewalLoading(false);
    }
  };

  const upgradeUserToLifetime = async (userId: string) => {
    try {
      console.log('🔄 Starting lifetime upgrade for user:', userId);
      
      // First, let's test if the function is accessible
      console.log('🧪 Testing RPC function accessibility...');
      
      const { data, error } = await supabase
        .rpc('upgrade_user_to_lifetime', { 
          user_id_param: userId
        });
      
      console.log('📊 RPC Response:', { data, error });
      
      if (error) {
        console.error('❌ RPC Error:', error);
        console.error('❌ RPC Error Details:', {
          message: error.message,
          code: error.code,
          details: error.details,
          hint: error.hint,
          fullError: JSON.stringify(error, null, 2)
        });
        
        // Fallback: Try to upgrade directly via table update
        console.log('🔄 Trying fallback direct table update...');
        await upgradeUserToLifetimeFallback(userId);
        return;
      }
      
      console.log('✅ Lifetime upgrade successful:', data);
      setSuccessMessageWithTimeout(`User upgraded to lifetime successfully: ${data}`);
      
      // Reload data to show updated status
      loadAdminData();
    } catch (error: any) {
      console.error('❌ Error upgrading user to lifetime:', error);
      console.error('❌ Error details:', {
        message: error.message,
        code: error.code,
        details: error.details,
        hint: error.hint,
        fullError: JSON.stringify(error, null, 2)
      });
      
      // Try fallback method
      try {
        console.log('🔄 Trying fallback method after error...');
        await upgradeUserToLifetimeFallback(userId);
      } catch (fallbackError: any) {
        console.error('❌ Fallback also failed:', fallbackError);
        setErrorWithTimeout(`Failed to upgrade user to lifetime: ${error.message}`);
      }
    }
  };

  const upgradeUserToLifetimeFallback = async (userId: string) => {
    try {
      console.log('🔄 Using fallback method to upgrade user to lifetime...');
      
      // Update user profile directly
      const { error: updateError } = await supabase
        .from('user_profiles')
        .update({
          user_type: 'lifetime',
          subscription_status: 'active',
          subscription_start_date: new Date().toISOString(),
          subscription_end_date: '2099-12-31',
          max_offers: -1,
          has_analytics: true,
          has_ecommerce: true,
          can_change_background: true
        })
        .eq('user_id', userId);
      
      if (updateError) {
        console.error('❌ Fallback update failed:', updateError);
        throw updateError;
      }
      
      console.log('✅ Fallback lifetime upgrade successful!');
      setSuccessMessageWithTimeout('User upgraded to lifetime successfully using fallback method!');
      
      // Reload data to show updated status
      loadAdminData();
    } catch (error: any) {
      console.error('❌ Fallback method failed:', error);
      throw error;
    }
  };

  const testLifetimeFunction = async () => {
    // This function is no longer needed - removed for cleaner code
  };

  const checkCurrentUserStatus = async () => {
    // This function is no longer needed - removed for cleaner code
  };

  const filteredUsers = users.filter(user => {
    const searchableText = (user.email || user.id || '').toLowerCase();
    const matchesSearch = searchableText.includes(searchTerm.toLowerCase());
    const matchesFilter = filterType === 'all' || user.profile?.userType === filterType;
    return matchesSearch && matchesFilter;
  });

  if (!hasPermission('admin')) {
    return (
      <div className="text-center py-12">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h2>
        <p className="text-gray-600">You don't have permission to access the admin dashboard.</p>
        <div className="mt-4 p-4 bg-gray-100 rounded-lg text-left text-sm">
          <p><strong>Debug Info:</strong></p>
          <p>User ID: {session?.user?.id || 'No user ID'}</p>
          <p>User Type: {session?.user?.user_metadata?.userType || 'No user type'}</p>
          <p>Has Permission: {hasPermission('admin') ? 'Yes' : 'No'}</p>
          <p>Session: {session ? 'Present' : 'None'}</p>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="text-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">Loading admin dashboard...</p>
      </div>
    );
  }

  return (
    <div className="space-y-6 sm:space-y-8">
      {/* Success Alert */}
      {successMessage && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-green-800">{successMessage}</p>
            </div>
            <div className="ml-auto pl-3">
              <div className="-mx-1.5 -my-1.5">
                <button
                  type="button"
                  onClick={() => setSuccessMessage(null)}
                  className="inline-flex bg-green-50 rounded-md p-1.5 text-green-500 hover:bg-green-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-green-50 focus:ring-green-600"
                >
                  <span className="sr-only">Dismiss</span>
                  <svg className="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Error Alert */}
      {error && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-yellow-800">{error}</p>
            </div>
            <div className="ml-auto pl-3">
              <div className="-mx-1.5 -my-1.5">
                <button
                  type="button"
                  onClick={() => setError(null)}
                  className="inline-flex bg-yellow-50 rounded-md p-1.5 text-yellow-500 hover:bg-yellow-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-yellow-50 focus:ring-yellow-600"
                >
                  <span className="sr-only">Dismiss</span>
                  <svg className="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Header with essential controls */}
      <div className="bg-white rounded-xl shadow-lg p-6 mb-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Admin Dashboard</h1>
            <p className="text-gray-600 mt-1">Manage users, subscriptions, and system settings</p>
          </div>
          
          {/* Essential action buttons only */}
          <div className="flex flex-wrap gap-2">
            <button
              onClick={syncMissingUserProfiles}
              className="inline-flex items-center px-3 py-1 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              🔄 Sync User Profiles
            </button>
          </div>
        </div>
      </div>

      {/* Stats Cards with Refresh */}
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-semibold text-gray-900">System Overview</h2>
        <button
          onClick={loadAdminData}
          className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
        >
          🔄 Refresh Data
        </button>
      </div>
      
      {lastUpdated && (
        <div className="text-sm text-gray-500 mb-4">
          Last updated: {lastUpdated.toLocaleString()}
        </div>
      )}

      {/* Debug Panel for Analytics */}
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-3">Analytics Debug Info</h3>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
          <div>
            <strong>User Profiles:</strong> {stats.totalUsers}
            <br />
            <span className="text-gray-600">Raw count from database</span>
          </div>
          <div>
            <strong>Active Subscriptions:</strong> {stats.activeSubscriptions}
            <br />
            <span className="text-gray-600">Stripe subscriptions with status='active'</span>
          </div>
          <div>
            <strong>Total Subscriptions:</strong> {stats.totalSubscriptions || 0}
            <br />
            <span className="text-gray-600">All Stripe subscription statuses</span>
          </div>
          <div>
            <strong>Business Cards:</strong> {stats.totalCards}
            <br />
            <span className="text-gray-600">Total cards created</span>
          </div>
        </div>
        <div className="mt-3 p-3 bg-white rounded border">
          <strong>Revenue Calculation:</strong> ${stats.totalRevenue.toFixed(2)}
          <br />
          <span className="text-gray-600">Sum of all active subscription amounts</span>
        </div>
        {stats.subscriptionStatuses && Object.keys(stats.subscriptionStatuses).length > 0 && (
          <div className="mt-3 p-3 bg-white rounded border">
            <strong>Subscription Status Breakdown:</strong>
            <div className="mt-2 text-sm">
              {Object.entries(stats.subscriptionStatuses || {}).map(([status, count]) => (
                <div key={status} className="flex justify-between">
                  <span className="capitalize">{status}:</span>
                  <span className="font-medium">{count as number}</span>
                </div>
              ))}
            </div>
          </div>
        )}
        <div className="flex gap-2 mt-3">
          <button
            onClick={() => {
              console.log('Manual analytics refresh triggered');
              loadAdminData();
            }}
            className="inline-flex items-center px-3 py-1 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            🔄 Debug Refresh
          </button>
          <button
            onClick={analyzeSubscriptions}
            className="inline-flex items-center px-3 py-1 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            💳 Analyze Subscriptions
          </button>

        </div>
      </div>
      
      {/* Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-white rounded-xl shadow-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Total Users</h3>
            <Users className="w-6 h-6 text-blue-600" />
          </div>
          <div className="text-3xl font-bold text-gray-900">{stats.totalUsers}</div>
        </div>

        <div className="bg-white rounded-xl shadow-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Active Subscriptions</h3>
            <CreditCard className="w-6 h-6 text-green-600" />
          </div>
          <div className="text-3xl font-bold text-gray-900">{stats.activeSubscriptions}</div>
        </div>

        <div className="bg-white rounded-xl shadow-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Monthly Revenue</h3>
            <DollarSign className="w-6 h-6 text-yellow-600" />
          </div>
          <div className="text-3xl font-bold text-gray-900">${stats.totalRevenue.toFixed(0)}</div>
        </div>

        <div className="bg-white rounded-xl shadow-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Business Cards</h3>
            <BarChart3 className="w-6 h-6 text-purple-600" />
          </div>
          <div className="text-3xl font-bold text-gray-900">{stats.totalCards}</div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="border-b border-gray-200 mb-8">
        <nav className="-mb-px flex space-x-12">
          <button
            onClick={() => setActiveTab('overview')}
            className={`py-3 px-2 border-b-2 font-medium text-sm transition-colors ${
              activeTab === 'overview'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <BarChart3 className="w-4 h-4 inline mr-2" />
            Overview
          </button>
          <button
            onClick={() => setActiveTab('users')}
            className={`py-3 px-2 border-b-2 font-medium text-sm transition-colors ${
              activeTab === 'users'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <Users className="w-4 h-4 inline mr-2" />
            Users
          </button>
          <button
            onClick={() => setActiveTab('emails')}
            className={`py-3 px-2 border-b-2 font-medium text-sm transition-colors ${
              activeTab === 'emails'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <Mail className="w-4 h-4 inline mr-2" />
            Send Emails
          </button>
          <button
            onClick={() => setActiveTab('templates')}
            className={`py-3 px-2 border-b-2 font-medium text-sm transition-colors ${
              activeTab === 'templates'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <FileText className="w-4 h-4 inline mr-2" />
            Email Templates
          </button>
          <button
            onClick={() => setActiveTab('examples')}
            className={`py-3 px-2 border-b-2 font-medium text-sm transition-colors ${
              activeTab === 'examples'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <FileText className="w-4 h-4 inline mr-2" />
            Template Examples
          </button>
          <button
            onClick={() => setActiveTab('subscriptions')}
            className={`py-3 px-2 border-b-2 font-medium text-sm transition-colors ${
              activeTab === 'subscriptions'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <CreditCard className="w-4 h-4 inline mr-2" />
            Subscriptions
          </button>
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <div className="space-y-6">
          {/* Overview content - removed duplicate stats cards */}
          <div className="bg-white rounded-xl shadow-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Welcome to Admin Dashboard</h3>
            <p className="text-gray-600">
              Use the tabs above to manage different aspects of your system. The main statistics are displayed at the top of the dashboard.
            </p>
          </div>
        </div>
      )}

      {activeTab === 'users' && (
        <div className="space-y-6">
          {/* Users Management Content */}
          <div className="bg-white rounded-xl shadow-lg">
            <div className="p-4 sm:p-6 border-b border-gray-200">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h2 className="text-lg sm:text-xl font-semibold text-gray-900">User Management</h2>
                  <p className="text-sm text-gray-600 mt-1">
                    Total Users: {users.length} | Showing: {filteredUsers.length}
                  </p>
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={loadUsersFromEdgeFunction}
                    disabled={loading}
                    className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                    Refresh Users
                  </button>
                  <button
                    onClick={syncMissingUserProfiles}
                    disabled={loading}
                    className="inline-flex items-center px-3 py-2 border border-green-300 text-sm font-medium rounded-md text-green-700 bg-green-50 hover:bg-green-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    Fix Missing Profiles
                  </button>
                </div>
              </div>
              
              {/* Search and Filter */}
              <div className="flex flex-col space-y-4 sm:flex-row sm:space-y-0 sm:space-x-4">
                {/* Search */}
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 sm:w-5 sm:h-5 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search users by email or ID..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-9 sm:pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm sm:text-base"
                  />
                </div>

                {/* Tag Filter */}
                <div className="relative">
                  <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 sm:w-5 sm:h-5 text-gray-400" />
                  <select
                    value={filterType}
                    onChange={(e) => setFilterType(e.target.value as UserType | 'all')}
                    className="pl-9 sm:pl-10 pr-8 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm sm:text-base"
                  >
                    <option value="all">All Users</option>
                    <option value="free">Free</option>
                    <option value="unlimited_monthly">Unlimited Monthly</option>
                    <option value="unlimited_yearly">Unlimited Yearly</option>
                    <option value="super_unlimited">Super Unlimited</option>
                    <option value="lifetime">Lifetime</option>
                    <option value="super_admin">Super Admin</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Users Table - Mobile Responsive */}
            {filteredUsers.length === 0 ? (
              <div className="text-center py-12">
                <Users className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No users found</h3>
                <p className="mt-1 text-sm text-gray-500">
                  {searchTerm || filterType !== 'all' 
                    ? 'Try adjusting your search or filter criteria.'
                    : 'No users have been created yet.'
                  }
                </p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                {/* Desktop Table */}
                <div className="hidden sm:block">
                  <table className="w-full">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          User
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Plan
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Subscription End
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Joined
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {filteredUsers.map((user) => (
                        <tr key={user.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div>
                              <div className="text-sm font-medium text-gray-900">
                                {user.email || 'N/A'}
                              </div>
                              <div className="text-sm text-gray-500 truncate max-w-xs">{user.id}</div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            {user.profile ? (
                              <UserTypeIndicator userType={user.profile.userType} />
                            ) : (
                              <span className="text-sm text-gray-500">No profile</span>
                            )}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {user.profile && user.profile.subscriptionEndDate ? (
                              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                new Date(user.profile.subscriptionEndDate) < new Date() 
                                  ? 'bg-red-100 text-red-800' 
                                  : new Date(user.profile.subscriptionEndDate) < new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)
                                  ? 'bg-yellow-100 text-yellow-800'
                                  : 'bg-green-100 text-green-800'
                              }`}>
                                {new Date(user.profile.subscriptionEndDate).toLocaleDateString()}
                              </span>
                            ) : (
                              <span className="text-sm text-gray-400">No end date</span>
                            )}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {user.created_at ? new Date(user.created_at).toLocaleDateString() : 'N/A'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center space-x-2">
                              {user.profile ? (
                                <>
                                  <select
                                    value={user.profile.userType}
                                    onChange={(e) => updateUserType(user.id, e.target.value as UserType)}
                                    className="text-sm border border-gray-300 rounded px-2 py-1 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                  >
                                    <option value="free">Free</option>
                                    <option value="unlimited_monthly">Unlimited Monthly</option>
                                    <option value="unlimited_yearly">Unlimited Yearly</option>
                                    <option value="super_unlimited">Super Unlimited</option>
                                    <option value="lifetime">Lifetime</option>
                                    <option value="super_admin">Super Admin</option>
                                  </select>
                                  {user.profile && user.profile.userType !== 'free' && (
                                    <input
                                      type="date"
                                      min={new Date().toISOString().split('T')[0]}
                                      onChange={(e) => updateUserType(user.id, user.profile!.userType, e.target.value)}
                                      className="text-sm border border-gray-300 rounded px-2 py-1 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                      placeholder="End Date"
                                      title="Set subscription end date"
                                    />
                                  )}
                                  <button
                                    onClick={() => deleteUser(user.id)}
                                    disabled={deletingUsers.has(user.id)}
                                    className="text-red-600 hover:text-red-900 hover:bg-red-50 p-1 rounded transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                                    title="Delete User"
                                  >
                                    {deletingUsers.has(user.id) ? (
                                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600"></div>
                                    ) : (
                                      <Trash2 className="h-4 w-4" />
                                    )}
                                  </button>
                                  <button
                                    onClick={() => openRenewalModal(user.id, user.profile?.userType || 'free')}
                                    className="text-green-600 hover:text-green-900 hover:bg-green-50 p-1 rounded transition-colors"
                                    title="Renew Subscription"
                                  >
                                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                    </svg>
                                  </button>
                                  {user.profile && user.profile.userType !== 'lifetime' && user.profile.userType !== 'super_admin' && (
                                    <button
                                      onClick={() => upgradeUserToLifetime(user.id)}
                                      className="text-purple-600 hover:text-purple-900 hover:bg-purple-50 p-1 rounded transition-colors"
                                      title="Upgrade to Lifetime Plan"
                                    >
                                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v13m0-13V6a2 2 0 112 2h-2z" />
                                      </svg>
                                    </button>
                                  )}
                                </>
                              ) : (
                                <>
                                  <button
                                    onClick={() => createUserProfile(user.id)}
                                    className="inline-flex items-center px-2 py-1 border border-green-300 text-xs font-medium rounded text-green-700 bg-green-50 hover:bg-green-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                                  >
                                    <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                    </svg>
                                    Create Profile
                                  </button>
                                  <button
                                    onClick={() => deleteUser(user.id)}
                                    disabled={deletingUsers.has(user.id)}
                                    className="text-red-600 hover:text-red-900 hover:bg-red-50 p-1 rounded transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                                    title="Delete User"
                                  >
                                    {deletingUsers.has(user.id) ? (
                                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600"></div>
                                    ) : (
                                      <Trash2 className="h-4 w-4" />
                                    )}
                                  </button>
                                </>
                              )}
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                {/* Mobile Cards */}
                <div className="sm:hidden">
                  <div className="space-y-4 p-4">
                    {filteredUsers.map((user) => (
                      <div key={user.id} className="bg-gray-50 rounded-lg p-4 space-y-3">
                        <div>
                          <div className="font-medium text-gray-900 text-sm">
                            {user.email || 'N/A'}
                          </div>
                          <div className="text-xs text-gray-500 break-all">{user.id}</div>
                        </div>
                        
                        <div className="flex items-center justify-between">
                          <div>
                            {user.profile ? (
                              <UserTypeIndicator userType={user.profile.userType} className="text-xs" />
                            ) : (
                              <span className="text-xs text-gray-500">No profile</span>
                            )}
                          </div>
                          <div className="text-xs text-gray-500">
                            {user.profile && user.profile.subscriptionEndDate ? (
                              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                new Date(user.profile.subscriptionEndDate) < new Date() 
                                  ? 'bg-red-100 text-red-800' 
                                  : new Date(user.profile.subscriptionEndDate) < new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)
                                  ? 'bg-yellow-100 text-yellow-800'
                                  : 'bg-green-100 text-green-800'
                              }`}>
                                {new Date(user.profile.subscriptionEndDate).toLocaleDateString()}
                              </span>
                            ) : (
                              <span className="text-sm text-gray-400">No end date</span>
                            )}
                          </div>
                        </div>

                        <div>
                          {user.created_at ? new Date(user.created_at).toLocaleDateString() : 'N/A'}
                        </div>

                        <div>
                          {user.profile ? (
                            <>
                              <label className="block text-xs font-medium text-gray-700 mb-1">
                                Change User Type:
                              </label>
                              <select
                                value={user.profile.userType}
                                onChange={(e) => updateUserType(user.id, e.target.value as UserType)}
                                className="w-full text-sm border border-gray-300 rounded px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                              >
                                <option value="free">Free</option>
                                <option value="unlimited_monthly">Unlimited Monthly</option>
                                <option value="unlimited_yearly">Unlimited Yearly</option>
                                <option value="super_unlimited">Super Unlimited</option>
                                <option value="lifetime">Lifetime</option>
                                <option value="super_admin">Super Admin</option>
                              </select>
                              {user.profile && user.profile.userType !== 'free' && (
                                <>
                                  <label className="block text-xs font-medium text-gray-700 mb-1 mt-2">
                                    Subscription End Date:
                                  </label>
                                  <input
                                    type="date"
                                    min={new Date().toISOString().split('T')[0]}
                                    onChange={(e) => updateUserType(user.id, user.profile!.userType, e.target.value)}
                                    className="w-full text-sm border border-gray-300 rounded px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                    placeholder="End Date"
                                    title="Set subscription end date"
                                  />
                                </>
                              )}
                              <button
                                onClick={() => openRenewalModal(user.id, user.profile?.userType || 'free')}
                                className="w-full mt-2 inline-flex items-center justify-center px-3 py-2 border border-green-300 text-xs font-medium rounded text-green-700 bg-green-50 hover:bg-green-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                              >
                                Renew Subscription
                              </button>
                              {user.profile && user.profile.userType !== 'lifetime' && user.profile.userType !== 'super_admin' && (
                                <button
                                  onClick={() => upgradeUserToLifetime(user.id)}
                                  className="w-full mt-2 inline-flex items-center justify-center px-3 py-2 border border-purple-300 text-xs font-medium rounded text-purple-700 bg-purple-50 hover:bg-purple-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
                                >
                                  ♾️ Upgrade to Lifetime
                                </button>
                              )}
                            </>
                          ) : (
                            <button
                              onClick={() => createUserProfile(user.id)}
                              className="w-full inline-flex items-center justify-center px-3 py-2 border border-green-300 text-xs font-medium rounded text-green-700 bg-green-50 hover:bg-green-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                            >
                              <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                              </svg>
                              Create Profile
                            </button>
                          )}
                          <button
                            onClick={() => deleteUser(user.id)}
                            disabled={deletingUsers.has(user.id)}
                            className="text-red-600 hover:text-red-900 text-xs mt-2 block disabled:opacity-50 disabled:cursor-not-allowed"
                          >
                            {deletingUsers.has(user.id) ? (
                              <div className="flex items-center justify-center">
                                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600 mr-2"></div>
                                Deleting...
                              </div>
                            ) : (
                              'Delete User'
                            )}
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {activeTab === 'emails' && (
        <div className="space-y-6">
          <AdminEmailDashboard />
        </div>
      )}

      {activeTab === 'templates' && (
        <div className="space-y-6">
          <EmailTemplateManager />
        </div>
      )}

      {activeTab === 'examples' && (
        <div className="space-y-6">
          <EmailTemplates />
        </div>
      )}

      {activeTab === 'subscriptions' && (
        <div className="space-y-6">
          <SubscriptionManager />
        </div>
      )}

      {/* Renewal Modal */}
      {showRenewalModal && renewalUser && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-8 max-w-md w-full mx-4">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">Renew Subscription</h3>
            
            <div className="mb-6">
              <p className="text-gray-600 mb-4">
                Renew subscription for user with <strong>{renewalUser.userType}</strong> plan
              </p>
              
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Renewal Duration (days)
              </label>
              <select
                value={renewalDuration}
                onChange={(e) => setRenewalDuration(Number(e.target.value))}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value={7}>7 days</option>
                <option value={30}>30 days (1 month)</option>
                <option value={90}>90 days (3 months)</option>
                <option value={180}>180 days (6 months)</option>
                <option value={365}>365 days (1 year)</option>
                <option value={730}>730 days (2 years)</option>
              </select>
            </div>
            
            <div className="flex space-x-3">
              <button
                onClick={() => {
                  setShowRenewalModal(false);
                  setRenewalUser(null);
                }}
                className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={handleRenewal}
                disabled={renewalLoading}
                className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
              >
                {renewalLoading ? 'Renewing...' : 'Renew Subscription'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}