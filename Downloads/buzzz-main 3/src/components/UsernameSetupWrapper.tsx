import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import UsernameSetupModal from './UsernameSetupModal';

interface UsernameSetupWrapperProps {
  children: React.ReactNode;
}

export default function UsernameSetupWrapper({ children }: UsernameSetupWrapperProps) {
  const { showUsernameSetup, setShowUsernameSetup, user, userProfile } = useAuth();
  const navigate = useNavigate();

  const handleUsernameSetupComplete = () => {
    setShowUsernameSetup(false);
    // Navigate to dashboard instead of reloading
    navigate('/dashboard');
  };

  const handleCloseUsernameSetup = () => {
    setShowUsernameSetup(false);
  };

  // Get user info from Google profile
  const getUserInfo = () => {
    if (!user) return { email: '', name: '' };
    
    return {
      email: user.email || '',
      name: user.user_metadata?.full_name || user.user_metadata?.name || ''
    };
  };

  return (
    <>
      {children}
      <UsernameSetupModal
        isOpen={showUsernameSetup}
        onClose={handleCloseUsernameSetup}
        onComplete={handleUsernameSetupComplete}
        userEmail={getUserInfo().email}
        userName={getUserInfo().name}
      />
    </>
  );
}
