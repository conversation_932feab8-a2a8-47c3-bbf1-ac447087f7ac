import React, { useState, useEffect } from 'react';
import { X, Mail, Lock, User, AlertCircle, Sparkles, Check, X as XIcon } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { supabase } from '../lib/supabase';
import { isUsernameReserved } from '../utils/usernameBlacklist';

interface AuthModalProps {
  isOpen: boolean;
  onClose: () => void;
  initialMode?: 'signin' | 'signup';
}

export default function AuthModal({ isOpen, onClose, initialMode = 'signin' }: AuthModalProps) {
  const [mode, setMode] = useState<'signin' | 'signup'>(initialMode);
  const [email, setEmail] = useState('');
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [googleLoading, setGoogleLoading] = useState(false);
  const [error, setError] = useState('');
  const [usernameStatus, setUsernameStatus] = useState<'idle' | 'checking' | 'available' | 'taken'>('idle');

  const { signIn, signUp, signInWithGoogle, user } = useAuth();
  const navigate = useNavigate();

  // Check username availability
  const checkUsernameAvailability = async (username: string) => {
    if (!username || username.length < 3) {
      setUsernameStatus('idle');
      return;
    }

    // Check if username is reserved first
    if (isUsernameReserved(username)) {
      setUsernameStatus('taken');
      return;
    }

    setUsernameStatus('checking');
    
    try {
      const { data: isAvailable, error } = await supabase
        .rpc('check_username_availability', { check_username: username });
      
      if (error) {
        console.error('Error checking username:', error);
        setUsernameStatus('taken');
        return;
      }
      
      setUsernameStatus(isAvailable ? 'available' : 'taken');
    } catch (err) {
      console.error('Error checking username availability:', err);
      setUsernameStatus('taken');
    }
  };

  // Redirect to dashboard when user successfully logs in
  useEffect(() => {
    if (user && isOpen) {
      onClose();
      navigate('/dashboard');
    }
  }, [user, isOpen, onClose, navigate]);

  // Debounced username availability check
  useEffect(() => {
    if (mode === 'signup' && username) {
      const timeoutId = setTimeout(() => {
        checkUsernameAvailability(username);
      }, 500);

      return () => clearTimeout(timeoutId);
    } else {
      setUsernameStatus('idle');
    }
  }, [username, mode]);

  if (!isOpen) return null;

  const getErrorMessage = (error: any) => {
    if (!error) return '';
    
    const message = error.message || '';
    
    // Handle specific Supabase auth errors
    if (message.includes('Invalid login credentials') || message.includes('invalid_credentials')) {
      return mode === 'signin' 
        ? 'The email or password you entered is incorrect. Please check your credentials and try again.'
        : 'Unable to create account with these credentials.';
    }
    
    if (message.includes('User already registered')) {
      return 'An account with this email already exists. Try signing in instead.';
    }
    
    if (message.includes('Email not confirmed')) {
      return 'Please check your email and click the confirmation link before signing in.';
    }
    
    if (message.includes('Password should be at least')) {
      return 'Password must be at least 6 characters long.';
    }
    
    if (message.includes('Invalid email')) {
      return 'Please enter a valid email address.';
    }
    
    if (message.includes('Username is already taken')) {
      return 'Username is already taken. Please choose a different one.';
    }
    
    // Return the original message for other errors
    return message;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    if (mode === 'signup') {
      // Validate password match
      if (password !== confirmPassword) {
        setError('Passwords do not match');
        setLoading(false);
        return;
      }

      // Validate username format
      if (username) {
        const usernameRegex = /^[a-zA-Z0-9_-]+$/;
        if (!usernameRegex.test(username)) {
          setError('Username can only contain letters, numbers, hyphens, and underscores');
          setLoading(false);
          return;
        }

        if (username.length < 3 || username.length > 30) {
          setError('Username must be between 3 and 30 characters');
          setLoading(false);
          return;
        }

        if (username.startsWith('-') || username.startsWith('_') || 
            username.endsWith('-') || username.endsWith('_')) {
          setError('Username cannot start or end with hyphens or underscores');
          setLoading(false);
          return;
        }

        // Check if username is reserved
        if (isUsernameReserved(username)) {
          setError('This username is reserved and cannot be used. Please choose a different one.');
          setLoading(false);
          return;
        }

        // Check if username is available
        if (usernameStatus === 'taken') {
          setError('Username is already taken. Please choose a different one.');
          setLoading(false);
          return;
        }

        if (usernameStatus === 'checking') {
          setError('Please wait while we check username availability.');
          setLoading(false);
          return;
        }
      }
    }

    try {
      if (mode === 'signin') {
        const { error } = await signIn(email, password);
        if (error) {
          setError(getErrorMessage(error));
        } else {
          // Clear form data on success
          setEmail('');
          setPassword('');
          // For signin, the redirect will happen automatically via useEffect
        }
      } else {
        // Signup flow
        const result = await signUp(email, password, username);
        
        if (result.error) {
          setError(getErrorMessage(result.error));
        } else if (result.success && result.requiresVerification) {
          // Clear form data on success
          setEmail('');
          setUsername('');
          setPassword('');
          setConfirmPassword('');
          
          // Show success message and redirect to verification pending page
          setError('');
          onClose(); // Close the modal
          
          // Redirect to verification pending page
          window.location.href = `/verify-email?email=${encodeURIComponent(email)}&pending=true`;
        }
      }
    } catch (err) {
      setError('An unexpected error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleGoogleSignIn = async () => {
    setError('');
    setGoogleLoading(true);

    try {
      const { error } = await signInWithGoogle();
      
      if (error) {
        setError(getErrorMessage(error));
      }
      // Success will be handled by the useEffect hook when user state changes
    } catch (err) {
      setError('Failed to sign in with Google. Please try again.');
    } finally {
      setGoogleLoading(false);
    }
  };

  const toggleMode = () => {
    setMode(mode === 'signin' ? 'signup' : 'signin');
    setError('');
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-3xl shadow-large w-full max-w-md transform animate-scale-in">
        {/* Header */}
        <div className="relative p-8 pb-6">
          <div className="absolute inset-0 bg-gradient-to-br from-primary-50 to-secondary-50 rounded-t-3xl"></div>
          <div className="relative flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold text-neutral-900 flex items-center">
                <Sparkles className="w-6 h-6 mr-2 text-primary-500" />
                {mode === 'signin' ? 'Welcome Back' : 'Join Us'}
              </h2>
              <p className="text-neutral-600 mt-1">
                {mode === 'signin' 
                  ? 'Sign in to your account' 
                  : 'Create your digital business card'
                }
              </p>
            </div>
            <button
              onClick={onClose}
              className="w-10 h-10 bg-white/80 backdrop-blur-sm rounded-2xl flex items-center justify-center hover:bg-white transition-all duration-200 transform hover:scale-105"
            >
              <X className="w-5 h-5 text-neutral-600" />
            </button>
          </div>
        </div>

        {/* Form */}
        <div className="px-8 pb-8 space-y-6">
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-2xl p-4 flex items-start animate-slide-in">
              <AlertCircle className="w-5 h-5 text-red-500 mr-3 flex-shrink-0 mt-0.5" />
              <div className="flex-1">
                <p className="text-red-700 text-sm font-medium">{error}</p>
                {mode === 'signin' && error.includes('email or password') && (
                  <p className="text-red-600 text-xs mt-2">
                    Don't have an account yet?{' '}
                    <button
                      type="button"
                      onClick={toggleMode}
                      className="underline hover:no-underline font-medium"
                    >
                      Sign up here
                    </button>
                  </p>
                )}
              </div>
            </div>
          )}

          {/* Google Sign In Button */}
          <button
            onClick={handleGoogleSignIn}
            disabled={googleLoading || loading}
            className="w-full bg-white border-2 border-neutral-200 text-neutral-700 p-4 rounded-2xl hover:border-neutral-300 hover:bg-neutral-50 transition-all duration-200 font-medium shadow-soft transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
          >
            {googleLoading ? (
              <div className="flex items-center">
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-neutral-600 mr-2"></div>
                Signing in...
              </div>
            ) : (
              <div className="flex items-center">
                <svg className="w-5 h-5 mr-3" viewBox="0 0 24 24">
                  <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                  <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                  <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                  <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                </svg>
                Continue with Google
              </div>
            )}
          </button>

          {/* Divider */}
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-neutral-200"></div>
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-4 bg-white text-neutral-500 font-medium">Or continue with email</span>
            </div>
          </div>

          <form onSubmit={handleSubmit} className="space-y-5">
            <div>
              <label className="block text-sm font-semibold text-neutral-700 mb-2">
                Email Address
              </label>
              <div className="relative">
                <Mail className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-neutral-400" />
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  className="w-full pl-12 pr-4 py-4 border border-neutral-200 rounded-2xl focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200 bg-neutral-50 focus:bg-white"
                  placeholder="Enter your email"
                />
              </div>
            </div>

            {mode === 'signup' && (
              <div>
                <label className="block text-sm font-semibold text-neutral-700 mb-2">
                  Username
                </label>
                <div className="relative">
                  <User className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-neutral-400" />
                  <input
                    type="text"
                    value={username}
                    onChange={(e) => setUsername(e.target.value)}
                    required
                    minLength={3}
                    maxLength={30}
                    pattern="[a-zA-Z0-9_-]+"
                    className={`w-full pl-12 pr-12 py-4 border rounded-2xl focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200 bg-neutral-50 focus:bg-white ${
                      usernameStatus === 'available' 
                        ? 'border-green-300 focus:ring-green-500' 
                        : usernameStatus === 'taken' 
                        ? 'border-red-300 focus:ring-red-500' 
                        : 'border-neutral-200 focus:ring-primary-500'
                    }`}
                    placeholder="Choose a username (3-30 characters)"
                  />
                  {usernameStatus === 'checking' && (
                    <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-neutral-400"></div>
                    </div>
                  )}
                  {usernameStatus === 'available' && (
                    <Check className="absolute right-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-green-500" />
                  )}
                  {usernameStatus === 'taken' && (
                    <XIcon className="absolute right-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-red-500" />
                  )}
                </div>
                <div className="flex items-center justify-between mt-1">
                  <p className="text-xs text-neutral-500">
                    Only letters, numbers, hyphens, and underscores allowed
                  </p>
                  {usernameStatus === 'available' && (
                    <p className="text-xs text-green-600 font-medium">✓ Available</p>
                  )}
                  {usernameStatus === 'taken' && (
                    <p className="text-xs text-red-600 font-medium">✗ Already taken</p>
                  )}
                </div>
              </div>
            )}

            <div>
              <label className="block text-sm font-semibold text-neutral-700 mb-2">
                Password
              </label>
              <div className="relative">
                <Lock className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-neutral-400" />
                <input
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  minLength={6}
                  className="w-full pl-12 pr-4 py-4 border border-neutral-200 rounded-2xl focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200 bg-neutral-50 focus:bg-white"
                  placeholder="Enter your password"
                />
              </div>
            </div>

            {mode === 'signup' && (
              <div>
                <label className="block text-sm font-semibold text-neutral-700 mb-2">
                  Confirm Password
                </label>
                <div className="relative">
                  <Lock className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-neutral-400" />
                  <input
                    type="password"
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    required
                    minLength={6}
                    className="w-full pl-12 pr-4 py-4 border border-neutral-200 rounded-2xl focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200 bg-neutral-50 focus:bg-white"
                    placeholder="Confirm your password"
                  />
                </div>
              </div>
            )}

            <button
              type="submit"
              disabled={loading || googleLoading}
              className="w-full bg-gradient-to-r from-primary-500 to-primary-600 text-white py-4 px-6 rounded-2xl hover:from-primary-600 hover:to-primary-700 transition-all duration-200 font-semibold text-lg disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105 shadow-colored"
            >
              {loading ? (
                <div className="flex items-center justify-center">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                  Please wait...
                </div>
              ) : (
                mode === 'signin' ? 'Sign In' : 'Create Account'
              )}
            </button>
          </form>

          <div className="text-center">
            <button
              type="button"
              onClick={toggleMode}
              className="text-primary-600 hover:text-primary-700 transition-colors font-semibold"
            >
              {mode === 'signin' 
                ? "Don't have an account? Sign up" 
                : "Already have an account? Sign in"
              }
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}