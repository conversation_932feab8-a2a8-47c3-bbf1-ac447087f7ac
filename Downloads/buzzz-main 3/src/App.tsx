import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import UsernameSetupWrapper from './components/UsernameSetupWrapper';
import HomePage from './pages/HomePage';
import Dashboard from './pages/Dashboard';
import BusinessCardView from './pages/BusinessCardView';
import ProtectedRoute from './components/ProtectedRoute';
import ContactUs from './pages/ContactUs';
import PrivacyPolicy from './pages/PrivacyPolicy';
import TermsOfService from './pages/TermsOfService';
import FAQ from './pages/FAQ';
import EmailVerification from './pages/EmailVerification';
import SubscriptionRenewal from './pages/SubscriptionRenewal';
import UsernameRoute from './components/UsernameRoute';

function App() {
  return (
    <AuthProvider>
      <Router>
        <UsernameSetupWrapper>
          <Routes>
            <Route path="/" element={<HomePage />} />
            <Route 
              path="/dashboard" 
              element={
                <ProtectedRoute>
                  <Dashboard />
                </ProtectedRoute>
              } 
            />
            <Route path="/contact" element={<ContactUs />} />
            <Route path="/privacy" element={<PrivacyPolicy />} />
            <Route path="/terms" element={<TermsOfService />} />
            <Route path="/faq" element={<FAQ />} />
            <Route path="/verify-email" element={<EmailVerification />} />
            <Route path="/subscription-renewal" element={<SubscriptionRenewal />} />
            <Route path="/account" element={<Navigate to="/" replace />} />
            <Route path="/account/renew" element={<SubscriptionRenewal />} />
            <Route path="/renew" element={<SubscriptionRenewal />} />
            {/* Support both @username and direct username formats - but exclude specific paths */}
            <Route path="/@:username" element={<BusinessCardView />} />
            {/* Username route - but exclude paths that start with specific prefixes */}
            <Route path="/:username" element={<UsernameRoute />} />
            {/* Custom domain support - this will catch any domain that doesn't match other routes */}
            <Route path="*" element={<BusinessCardView />} />
          </Routes>
        </UsernameSetupWrapper>
      </Router>
    </AuthProvider>
  );
}

export default App;