-- Drop and recreate functions with correct return types
-- PostgreSQL cannot change return types of existing functions

-- Drop existing functions
DROP FUNCTION IF EXISTS get_users_expiring_soon(integer);
DROP FUNCTION IF EXISTS get_recently_downgraded_users(integer);

-- Recreate get_users_expiring_soon function with correct types
CREATE OR REPLACE FUNCTION get_users_expiring_soon(days_ahead integer DEFAULT 7)
RETURNS TABLE(
  user_id uuid,
  email character varying(255),
  user_type text,
  subscription_end_date timestamptz,
  days_until_expiry integer
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    up.user_id,
    u.email,
    up.user_type,
    up.subscription_end_date,
    EXTRACT(DAY FROM (up.subscription_end_date - NOW()))::integer as days_until_expiry
  FROM user_profiles up
  JOIN auth.users u ON up.user_id = u.id
  WHERE up.user_type IN ('unlimited_monthly', 'unlimited_yearly', 'super_unlimited')
    AND up.subscription_status IN ('active', 'inactive')
    AND up.subscription_end_date BETWEEN NOW() AND NOW() + (days_ahead || ' days')::interval
    AND up.subscription_end_date IS NOT NULL
  ORDER BY up.subscription_end_date ASC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Recreate get_recently_downgraded_users function with correct types
CREATE OR REPLACE FUNCTION get_recently_downgraded_users(days_back integer DEFAULT 30)
RETURNS TABLE(
  user_id uuid,
  email character varying(255),
  old_user_type text,
  downgraded_at timestamptz,
  days_since_downgrade integer
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    up.user_id,
    u.email,
    up.user_type as old_user_type,
    up.updated_at as downgraded_at,
    EXTRACT(DAY FROM (NOW() - up.updated_at))::integer as days_since_downgrade
  FROM user_profiles up
  JOIN auth.users u ON up.user_id = u.id
  WHERE up.user_type = 'free'
    AND up.subscription_status = 'expired'
    AND up.updated_at >= NOW() - (days_back || ' days')::interval
    AND EXISTS (
      SELECT 1 FROM user_profiles_history uph 
      WHERE uph.user_id = up.user_id 
      AND uph.user_type IN ('unlimited_monthly', 'unlimited_yearly', 'super_unlimited')
    )
  ORDER BY up.updated_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
