/*
  # Restore Super Admin Status

  This migration restores super admin status for accounts that were incorrectly
  downgraded by the automatic system, and ensures super admins are properly protected.

  Changes:
  - Restore super admin status for specific admin emails
  - Set permanent subscription dates for super admins
  - Ensure super admins are excluded from future automatic downgrades
*/

-- First, let's identify and restore super admin accounts
DO $$
DECLARE
  admin_emails text[] := ARRAY['<EMAIL>', '<EMAIL>']; -- Add your admin emails here
  updated_count integer;
BEGIN
  -- Restore super admin status for specified emails
  UPDATE user_profiles 
  SET 
    user_type = 'super_admin',
    subscription_status = 'active',
    subscription_start_date = CURRENT_DATE,
    subscription_end_date = '2099-12-31'::date,
    max_offers = -1,
    has_analytics = true,
    has_ecommerce = true,
    can_change_background = true,
    updated_at = now()
  WHERE user_id IN (
    SELECT au.id FROM auth.users au 
    WHERE au.email = ANY(admin_emails)
  );
  
  GET DIAGNOSTICS updated_count = ROW_COUNT;
  RAISE NOTICE 'Restored super admin status for % accounts', updated_count;
  
  -- Also restore any existing super_admin users that might have been affected
  UPDATE user_profiles 
  SET 
    subscription_status = 'active',
    subscription_end_date = '2099-12-31'::date,
    updated_at = now()
  WHERE user_type = 'super_admin' 
    AND (subscription_status != 'active' OR subscription_end_date IS NULL OR subscription_end_date < '2099-01-01'::date);
  
  GET DIAGNOSTICS updated_count = ROW_COUNT;
  RAISE NOTICE 'Updated % existing super admin accounts', updated_count;
END $$;

-- Log the restoration in user_profiles_history
INSERT INTO user_profiles_history (
  user_id,
  user_type,
  subscription_status,
  max_offers,
  has_analytics,
  has_ecommerce,
  can_change_background,
  change_reason
)
SELECT 
  up.user_id,
  up.user_type,
  up.subscription_status,
  up.max_offers,
  up.has_analytics,
  up.has_ecommerce,
  up.can_change_background,
  'Super admin status restored - account was incorrectly downgraded'
FROM user_profiles up
WHERE up.user_type = 'super_admin'
  AND up.subscription_end_date = '2099-12-31'::date
  AND NOT EXISTS (
    SELECT 1 FROM user_profiles_history uph 
    WHERE uph.user_id = up.user_id 
      AND uph.change_reason LIKE '%Super admin status restored%'
  );

-- Verify the restoration
DO $$
DECLARE
  super_admin_count integer;
BEGIN
  SELECT COUNT(*) INTO super_admin_count
  FROM user_profiles 
  WHERE user_type = 'super_admin' 
    AND subscription_status = 'active' 
    AND subscription_end_date = '2099-12-31'::date;
  
  RAISE NOTICE 'Total super admin accounts after restoration: %', super_admin_count;
END $$;
