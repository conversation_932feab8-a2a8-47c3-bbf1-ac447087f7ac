-- Fix timestamp type mismatch in get_all_users_subscription_status function
-- The function returns timestamp with time zone but frontend expects date

DROP FUNCTION IF EXISTS get_all_users_subscription_status();

CREATE OR REPLACE FUNCTION get_all_users_subscription_status()
RETURNS TABLE(
  user_id uuid,
  email text,
  user_type text,
  subscription_status text,
  subscription_start_date date,
  subscription_end_date date,
  days_until_expiry integer,
  is_expired boolean
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    up.user_id,
    au.email::text,
    up.user_type,
    up.subscription_status,
    up.subscription_start_date::date,
    up.subscription_end_date::date,
    CASE 
      WHEN up.subscription_end_date IS NULL THEN NULL
      ELSE (up.subscription_end_date::date - CURRENT_DATE)
    END as days_until_expiry,
    CASE 
      WHEN up.subscription_end_date IS NULL THEN false
      ELSE up.subscription_end_date::date <= CURRENT_DATE
    END as is_expired
  FROM user_profiles up
  JOIN auth.users au ON up.user_id = au.id
  ORDER BY 
    CASE WHEN up.subscription_end_date IS NULL THEN 1 ELSE 0 END,
    up.subscription_end_date ASC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users (admins)
GRANT EXECUTE ON FUNCTION get_all_users_subscription_status() TO authenticated;
