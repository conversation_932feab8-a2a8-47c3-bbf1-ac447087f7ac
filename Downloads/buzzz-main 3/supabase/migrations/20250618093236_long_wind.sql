/*
  # Create saved_cards table

  1. New Tables
    - `saved_cards`
      - `id` (uuid, primary key)
      - `user_id` (uuid, foreign key to users)
      - `saved_card_data` (jsonb, stores the business card data)
      - `saved_at` (timestamp, when the card was saved)
      - `notes` (text, optional user notes)
      - `tags` (text array, optional tags for organization)
      - `updated_at` (timestamp, last update time)

  2. Security
    - Enable RLS on `saved_cards` table
    - Add policies for authenticated users to manage their own saved cards
    - Users can SELECT, INSERT, UPDATE, and DELETE their own saved cards

  3. Indexes
    - Index on user_id for efficient queries
    - Index on saved_at for ordering
*/

CREATE TABLE IF NOT EXISTS public.saved_cards (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    saved_card_data jsonb NOT NULL,
    saved_at timestamptz DEFAULT now() NOT NULL,
    notes text,
    tags text[],
    updated_at timestamptz DEFAULT now() NOT NULL
);

-- Enable Row Level Security
ALTER TABLE public.saved_cards ENABLE ROW LEVEL SECURITY;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_saved_cards_user_id ON public.saved_cards USING btree (user_id);
CREATE INDEX IF NOT EXISTS idx_saved_cards_saved_at ON public.saved_cards USING btree (saved_at DESC);

-- RLS Policies
CREATE POLICY "Users can view their own saved cards"
  ON public.saved_cards
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own saved cards"
  ON public.saved_cards
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own saved cards"
  ON public.saved_cards
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their own saved cards"
  ON public.saved_cards
  FOR DELETE
  TO authenticated
  USING (auth.uid() = user_id);