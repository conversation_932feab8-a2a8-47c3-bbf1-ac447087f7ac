-- Create a simple test function to verify <PERSON><PERSON> is working
CREATE OR REP<PERSON>CE FUNCTION test_rpc_function()
RETURNS TABLE(
  test_id integer,
  test_message text
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    1 as test_id,
    'RPC is working' as test_message;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- <PERSON> execute permission to authenticated users
GRANT EXECUTE ON FUNCTION test_rpc_function() TO authenticated;
