/*
  # Automatic Subscription Downgrade System

  This migration adds functionality to automatically downgrade users to the free plan
  when their paid subscriptions expire. This ensures users cannot access premium
  features without an active subscription.

  Features Added:
  - Function to check and downgrade expired subscriptions
  - Scheduled job to run daily (via cron or manual trigger)
  - Email notification system for downgraded users
  - Proper feature reset to free plan limits
*/

-- Function to automatically downgrade expired subscriptions
CREATE OR REPLACE FUNCTION auto_downgrade_expired_subscriptions()
RETURNS TABLE(
  user_id uuid,
  old_user_type text,
  new_user_type text,
  downgraded_at timestamptz
) AS $$
DECLARE
  user_record RECORD;
  downgrade_count integer := 0;
BEGIN
  -- Find all users with expired subscriptions that haven't been downgraded yet
  FOR user_record IN
    SELECT 
      up.user_id,
      up.user_type,
      up.subscription_status,
      up.subscription_end_date
    FROM user_profiles up
    WHERE up.user_type IN ('unlimited_monthly', 'unlimited_yearly', 'super_unlimited')
      AND up.subscription_status IN ('active', 'inactive')
      AND up.subscription_end_date < NOW()
      AND up.subscription_end_date IS NOT NULL
  LOOP
    -- Downgrade user to free plan
    UPDATE user_profiles
    SET 
      user_type = 'free',
      subscription_status = 'expired',
      max_offers = 3,
      has_analytics = false,
      has_ecommerce = false,
      can_change_background = false,
      updated_at = NOW()
    WHERE user_id = user_record.user_id;
    
    -- Return the downgrade information
    user_id := user_record.user_id;
    old_user_type := user_record.user_type;
    new_user_type := 'free';
    downgraded_at := NOW();
    
    -- Log the downgrade
    RAISE LOG 'Auto-downgraded user % from % to % at %', 
      user_record.user_id, user_record.user_type, 'free', NOW();
    
    downgrade_count := downgrade_count + 1;
    
    RETURN NEXT;
  END LOOP;
  
  -- Log summary
  RAISE LOG 'Auto-downgrade completed: % users downgraded to free plan', downgrade_count;
  
  RETURN;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to manually trigger subscription downgrade check
CREATE OR REPLACE FUNCTION check_and_downgrade_subscriptions()
RETURNS text AS $$
DECLARE
  result_count integer;
  result_text text;
BEGIN
  -- Get count of users that will be downgraded
  SELECT COUNT(*) INTO result_count
  FROM user_profiles up
  WHERE up.user_type IN ('unlimited_monthly', 'unlimited_yearly', 'super_unlimited')
    AND up.subscription_status IN ('active', 'inactive')
    AND up.subscription_end_date < NOW()
    AND up.subscription_end_date IS NOT NULL;
  
  -- Perform the downgrade
  PERFORM auto_downgrade_expired_subscriptions();
  
  -- Return result message
  result_text := 'Subscription check completed. ' || result_count || ' users downgraded to free plan.';
  
  RETURN result_text;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get users that will be downgraded soon (for admin notifications)
CREATE OR REPLACE FUNCTION get_users_expiring_soon(days_ahead integer DEFAULT 7)
RETURNS TABLE(
  user_id uuid,
  email character varying(255),
  user_type text,
  subscription_end_date timestamptz,
  days_until_expiry integer
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    up.user_id,
    u.email,
    up.user_type,
    up.subscription_end_date,
    EXTRACT(DAY FROM (up.subscription_end_date - NOW()))::integer as days_until_expiry
  FROM user_profiles up
  JOIN auth.users u ON up.user_id = u.id
  WHERE up.user_type IN ('unlimited_monthly', 'unlimited_yearly', 'super_unlimited')
    AND up.subscription_status IN ('active', 'inactive')
    AND up.subscription_end_date BETWEEN NOW() AND NOW() + (days_ahead || ' days')::interval
    AND up.subscription_end_date IS NOT NULL
  ORDER BY up.subscription_end_date ASC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get recently downgraded users (for admin review)
CREATE OR REPLACE FUNCTION get_recently_downgraded_users(days_back integer DEFAULT 30)
RETURNS TABLE(
  user_id uuid,
  email character varying(255),
  old_user_type text,
  downgraded_at timestamptz,
  days_since_downgrade integer
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    up.user_id,
    u.email,
    up.user_type as old_user_type,
    up.updated_at as downgraded_at,
    EXTRACT(DAY FROM (NOW() - up.updated_at))::integer as days_since_downgrade
  FROM user_profiles up
  JOIN auth.users u ON up.user_id = u.id
  WHERE up.user_type = 'free'
    AND up.subscription_status = 'expired'
    AND up.updated_at >= NOW() - (days_back || ' days')::interval
    AND EXISTS (
      SELECT 1 FROM user_profiles_history uph 
      WHERE uph.user_id = up.user_id 
      AND uph.user_type IN ('unlimited_monthly', 'unlimited_yearly', 'super_unlimited')
    )
  ORDER BY up.updated_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a history table to track user type changes (optional but recommended)
CREATE TABLE IF NOT EXISTS user_profiles_history (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  user_type text NOT NULL,
  subscription_status text NOT NULL,
  max_offers integer NOT NULL,
  has_analytics boolean NOT NULL,
  has_ecommerce boolean NOT NULL,
  can_change_background boolean NOT NULL,
  changed_at timestamptz DEFAULT NOW(),
  change_reason text DEFAULT 'automatic'
);

-- Enable RLS on history table
ALTER TABLE user_profiles_history ENABLE ROW LEVEL SECURITY;

-- Policy for history table (admin access only)
CREATE POLICY "Admin can view user profile history"
  ON user_profiles_history
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles up 
      WHERE up.user_id = auth.uid() 
      AND up.user_type IN ('super_admin', 'super_unlimited')
    )
  );

-- Trigger to log user profile changes to history table
CREATE OR REPLACE FUNCTION log_user_profile_changes()
RETURNS TRIGGER AS $$
BEGIN
  -- Only log if user_type or subscription_status changed
  IF OLD.user_type != NEW.user_type OR OLD.subscription_status != NEW.subscription_status THEN
    INSERT INTO user_profiles_history (
      user_id,
      user_type,
      subscription_status,
      max_offers,
      has_analytics,
      has_ecommerce,
      can_change_background,
      change_reason
    ) VALUES (
      OLD.user_id,
      OLD.user_type,
      OLD.subscription_status,
      OLD.max_offers,
      OLD.has_analytics,
      OLD.has_ecommerce,
      OLD.can_change_background,
      CASE 
        WHEN NEW.user_type = 'free' AND OLD.user_type != 'free' THEN 'subscription_expired'
        WHEN NEW.user_type != 'free' AND OLD.user_type = 'free' THEN 'subscription_upgraded'
        ELSE 'manual_change'
      END
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for logging changes
DROP TRIGGER IF EXISTS log_user_profile_changes_trigger ON user_profiles;
CREATE TRIGGER log_user_profile_changes_trigger
  AFTER UPDATE ON user_profiles
  FOR EACH ROW
  EXECUTE FUNCTION log_user_profile_changes();

-- Grant execute permissions on functions
GRANT EXECUTE ON FUNCTION auto_downgrade_expired_subscriptions() TO service_role;
GRANT EXECUTE ON FUNCTION check_and_downgrade_subscriptions() TO service_role;
GRANT EXECUTE ON FUNCTION get_users_expiring_soon(integer) TO service_role;
GRANT EXECUTE ON FUNCTION get_recently_downgraded_users(integer) TO service_role;

-- Grant execute permissions to authenticated users for admin functions
GRANT EXECUTE ON FUNCTION get_users_expiring_soon(integer) TO authenticated;
GRANT EXECUTE ON FUNCTION get_recently_downgraded_users(integer) TO authenticated;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_user_profiles_subscription_end_date ON user_profiles(subscription_end_date);
CREATE INDEX IF NOT EXISTS idx_user_profiles_user_type_status ON user_profiles(user_type, subscription_status);
CREATE INDEX IF NOT EXISTS idx_user_profiles_history_user_id ON user_profiles_history(user_id);
CREATE INDEX IF NOT EXISTS idx_user_profiles_history_changed_at ON user_profiles_history(changed_at);

-- Add comment to document the new functionality
COMMENT ON FUNCTION auto_downgrade_expired_subscriptions() IS 'Automatically downgrades users with expired subscriptions to free plan';
COMMENT ON FUNCTION check_and_downgrade_subscriptions() IS 'Manual trigger to check and downgrade expired subscriptions';
COMMENT ON FUNCTION get_users_expiring_soon(integer) IS 'Returns users whose subscriptions will expire within specified days';
COMMENT ON FUNCTION get_recently_downgraded_users(integer) IS 'Returns recently downgraded users for admin review';
