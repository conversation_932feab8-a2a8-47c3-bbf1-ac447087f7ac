/*
  # Add username change tracking

  1. Changes
    - Add `last_username_change` field to user_profiles table
    - This will track when users last changed their username
    - Used to enforce the 100-day restriction

  2. Security
    - No additional RLS policies needed as existing policies cover this field
*/

DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'user_profiles' AND column_name = 'last_username_change'
  ) THEN
    ALTER TABLE user_profiles ADD COLUMN last_username_change timestamptz;
  END IF;
END $$;