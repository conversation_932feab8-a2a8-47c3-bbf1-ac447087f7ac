/*
  # Fix User Profile Creation Function

  This migration fixes the create_user_profile function to ensure it properly
  creates user profiles with all required fields and sets user_type to 'free' by default.

  Issues Fixed:
  - Multiple conflicting create_user_profile function definitions
  - Missing fields in user profile creation
  - Ensure user_type is always set to 'free' for new users
*/

-- Drop all existing triggers that might be conflicting
DROP TRIGGER IF EXISTS create_user_profile_trigger ON auth.users;
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;

-- Drop the existing function to recreate it properly
DROP FUNCTION IF EXISTS create_user_profile() CASCADE;

-- Create the proper user profile creation function
CREATE OR REPLACE FUNCTION create_user_profile()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.user_profiles (
    user_id,
    user_type,
    subscription_status,
    max_offers,
    has_analytics,
    has_ecommerce,
    can_change_background,
    username,
    created_at,
    updated_at
  )
  VALUES (
    NEW.id,
    'free',  -- Always set to 'free' for new users
    'active',
    3,       -- Default max offers for free users
    false,   -- No analytics for free users
    false,   -- No ecommerce for free users
    false,   -- No background changes for free users
    NULL,    -- Username will be set later via the pending username logic
    NOW(),
    NOW()
  );
  
  RETURN NEW;
EXCEPTION
  WHEN OTHERS THEN
    -- Log the error but don't fail the user creation
    RAISE LOG 'Error creating user profile for user %: %', NEW.id, SQLERRM;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create the trigger on auth.users table
CREATE TRIGGER create_user_profile_trigger
  AFTER INSERT ON auth.users
  FOR EACH ROW
  EXECUTE FUNCTION create_user_profile();

-- Ensure the service role can manage user profiles
GRANT ALL ON user_profiles TO service_role;

-- Add a policy to allow the system to insert user profiles during signup
DROP POLICY IF EXISTS "System can insert user profiles" ON user_profiles;
CREATE POLICY "System can insert user profiles"
  ON user_profiles
  FOR INSERT
  TO authenticated, anon
  WITH CHECK (true);

-- Ensure the service role can manage user profiles
DROP POLICY IF EXISTS "Service role can manage user profiles" ON user_profiles;
CREATE POLICY "Service role can manage user profiles"
  ON user_profiles
  FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);

-- Update any existing users who don't have a profile yet
INSERT INTO user_profiles (
  user_id,
  user_type,
  subscription_status,
  max_offers,
  has_analytics,
  has_ecommerce,
  can_change_background,
  username,
  created_at,
  updated_at
)
SELECT 
  u.id,
  'free',
  'active',
  3,
  false,
  false,
  false,
  NULL,
  NOW(),
  NOW()
FROM auth.users u
WHERE NOT EXISTS (
  SELECT 1 FROM user_profiles up WHERE up.user_id = u.id
)
ON CONFLICT (user_id) DO NOTHING;
