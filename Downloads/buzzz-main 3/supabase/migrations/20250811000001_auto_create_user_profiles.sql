-- Migration: Auto-create user_profiles for new signups
-- This ensures all users (including Gmail signups) get profile entries

-- Create a function to handle new user signups
-- This will be called manually or through Supabase Edge Functions
CREATE OR REPLACE FUNCTION public.handle_new_user(user_uuid UUID)
RETURNS BOOLEAN AS $$
BEGIN
  -- Check if user profile already exists
  IF EXISTS (SELECT 1 FROM public.user_profiles WHERE user_id = user_uuid) THEN
    RETURN FALSE; -- Profile already exists
  END IF;
  
  -- Insert a new user profile for the user
  INSERT INTO public.user_profiles (
    user_id,
    user_type,
    subscription_status,
    has_analytics,
    has_custom_domains,
    has_ecommerce,
    can_change_background,
    max_offers,
    created_at,
    updated_at
  ) VALUES (
    user_uuid,
    'user', -- Default user type for new signups
    'free', -- Default subscription status
    false,  -- Default analytics setting
    false,  -- Default custom domains setting
    false,  -- Default ecommerce setting
    true,   -- Default background change permission
    3,      -- Default max offers for free users
    NOW(),
    NOW()
  );
  
  RETURN TRUE; -- Profile created successfully
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to sync existing users who don't have profiles
CREATE OR REPLACE FUNCTION public.sync_missing_user_profiles()
RETURNS INTEGER AS $$
DECLARE
  missing_count INTEGER;
BEGIN
  -- Count users without profiles
  SELECT COUNT(*) INTO missing_count
  FROM auth.users u
  LEFT JOIN public.user_profiles up ON u.id = up.user_id
  WHERE up.user_id IS NULL;
  
  -- Insert profiles for missing users
  INSERT INTO public.user_profiles (
    user_id,
    user_type,
    subscription_status,
    has_analytics,
    has_custom_domains,
    has_ecommerce,
    can_change_background,
    max_offers,
    created_at,
    updated_at
  )
  SELECT 
    u.id,
    'user',
    'free',
    false,
    false,
    false,
    true,
    3,
    NOW(),
    NOW()
  FROM auth.users u
  LEFT JOIN public.user_profiles up ON u.id = up.user_id
  WHERE up.user_id IS NULL;
  
  RETURN missing_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.handle_new_user(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.sync_missing_user_profiles() TO authenticated;

-- Create an index to improve performance when checking for existing profiles
CREATE INDEX IF NOT EXISTS idx_user_profiles_user_id ON public.user_profiles(user_id);
