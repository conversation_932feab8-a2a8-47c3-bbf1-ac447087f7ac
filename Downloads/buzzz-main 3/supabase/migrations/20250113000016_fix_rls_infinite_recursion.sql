/*
  # Fix RLS Infinite Recursion

  This migration fixes the infinite recursion in RLS policies that's preventing
  super admins from accessing the system.

  Problem: RLS policies were referencing user_profiles table within their own
  conditions, causing infinite recursion when checking permissions.

  Solution: Simplify policies and remove circular references.
*/

-- Drop all problematic RLS policies that cause infinite recursion
DROP POLICY IF EXISTS "Super admins can view all profiles" ON user_profiles;
DROP POLICY IF EXISTS "Super admins can update all profiles" ON user_profiles;
DROP POLICY IF EXISTS "Super admins can insert profiles" ON user_profiles;
DROP POLICY IF EXISTS "Super admins can delete profiles" ON user_profiles;

-- Drop existing policies that might conflict
DROP POLICY IF EXISTS "Users can view own profile" ON user_profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON user_profiles;
DROP POLICY IF EXISTS "Users can insert own profile" ON user_profiles;
DROP POLICY IF EXISTS "System can insert profiles" ON user_profiles;
DROP POLICY IF EXISTS "Users can insert own profile" ON user_profiles;
DROP POLICY IF EXISTS "System can insert user profiles" ON user_profiles;
DROP POLICY IF EXISTS "Service role can manage user profiles" ON user_profiles;

-- Create simple, safe policies that don't cause recursion
-- Policy 1: Users can always view their own profile
CREATE POLICY "Users can view own profile"
  ON user_profiles
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

-- Policy 2: Users can update their own profile
CREATE POLICY "Users can update own profile"
  ON user_profiles
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

-- Policy 3: Users can insert their own profile
CREATE POLICY "Users can insert own profile"
  ON user_profiles
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

-- Policy 4: Allow system to insert profiles (for triggers)
CREATE POLICY "System can insert profiles"
  ON user_profiles
  FOR INSERT
  TO authenticated
  WITH CHECK (true);

-- Policy 5: Allow service role full access (bypasses RLS)
-- This is handled by SECURITY DEFINER functions, so no explicit policy needed

-- Verify the policies were created
DO $$
BEGIN
  RAISE NOTICE 'Successfully fixed RLS infinite recursion by creating simple, safe policies';
END $$;
